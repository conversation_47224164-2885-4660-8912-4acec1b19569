@echo off
echo ================================================================================
echo Serena MCP服务器安装脚本
echo ================================================================================
echo.

echo 🔍 检查系统环境...
git --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Git
    echo    请先安装Git: https://git-scm.com/downloads
    pause
    exit /b 1
)

python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python
    echo    请先安装Python 3.8+: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ 系统环境检查通过

echo.
echo 📥 克隆Serena仓库...
if exist "serena" (
    echo ℹ️  Serena目录已存在，跳过克隆
    cd serena
    git pull origin main
) else (
    git clone https://github.com/oraios/serena.git
    if errorlevel 1 (
        echo ❌ 克隆仓库失败
        pause
        exit /b 1
    )
    cd serena
)

echo ✅ 仓库克隆/更新完成

echo.
echo 📋 检查项目结构...
if exist "requirements.txt" (
    echo ✅ 发现Python项目 (requirements.txt)
    set PROJECT_TYPE=python
) else if exist "package.json" (
    echo ✅ 发现Node.js项目 (package.json)
    set PROJECT_TYPE=nodejs
) else (
    echo ⚠️  未找到标准配置文件，尝试Python安装
    set PROJECT_TYPE=python
)

echo.
if "%PROJECT_TYPE%"=="python" (
    echo 🐍 安装Python依赖...
    
    echo 创建虚拟环境...
    python -m venv serena-env
    if errorlevel 1 (
        echo ❌ 虚拟环境创建失败
        pause
        exit /b 1
    )
    
    echo 激活虚拟环境...
    call serena-env\Scripts\activate.bat
    
    echo 升级pip...
    python -m pip install --upgrade pip
    
    if exist "requirements.txt" (
        echo 安装依赖包...
        pip install -r requirements.txt
    ) else (
        echo 安装基本依赖...
        pip install fastapi uvicorn requests aiohttp
    )
    
    if exist "setup.py" (
        echo 安装项目...
        pip install -e .
    )
    
) else (
    echo 📦 安装Node.js依赖...
    npm install
    if errorlevel 1 (
        echo ❌ npm安装失败
        pause
        exit /b 1
    )
)

echo ✅ 依赖安装完成

echo.
echo ⚙️  配置Serena...
if exist "config.example.json" (
    if not exist "config.json" (
        copy "config.example.json" "config.json"
        echo ✅ 已创建config.json配置文件
    )
)

if exist ".env.example" (
    if not exist ".env" (
        copy ".env.example" ".env"
        echo ✅ 已创建.env环境文件
    )
)

echo.
echo 🧪 测试安装...
if "%PROJECT_TYPE%"=="python" (
    echo 测试Python安装...
    python -c "print('Python环境正常')" 2>nul
    if errorlevel 1 (
        echo ❌ Python测试失败
    ) else (
        echo ✅ Python测试通过
    )
) else (
    echo 测试Node.js安装...
    node -e "console.log('Node.js环境正常')" 2>nul
    if errorlevel 1 (
        echo ❌ Node.js测试失败
    ) else (
        echo ✅ Node.js测试通过
    )
)

echo.
echo 📝 创建启动脚本...
if "%PROJECT_TYPE%"=="python" (
    echo @echo off > start_serena.bat
    echo cd /d "%CD%" >> start_serena.bat
    echo call serena-env\Scripts\activate.bat >> start_serena.bat
    echo echo 🚀 启动Serena服务器... >> start_serena.bat
    echo python main.py >> start_serena.bat
    echo pause >> start_serena.bat
) else (
    echo @echo off > start_serena.bat
    echo cd /d "%CD%" >> start_serena.bat
    echo echo 🚀 启动Serena服务器... >> start_serena.bat
    echo npm start >> start_serena.bat
    echo pause >> start_serena.bat
)

echo ✅ 启动脚本已创建: start_serena.bat

echo.
echo 📋 安装集成依赖...
cd ..
if exist "venv\Scripts\activate.bat" (
    call venv\Scripts\activate.bat
    pip install requests
    echo ✅ 集成依赖安装完成
)

echo.
echo ================================================================================
echo 🎉 Serena安装完成!
echo ================================================================================
echo.
echo 📁 安装位置: %CD%\serena
echo.
echo 🚀 启动方法:
echo 1. 进入serena目录: cd serena
echo 2. 运行启动脚本: start_serena.bat
echo    或手动启动:
if "%PROJECT_TYPE%"=="python" (
    echo    - 激活环境: serena-env\Scripts\activate.bat
    echo    - 启动服务: python main.py
) else (
    echo    - 启动服务: npm start
)
echo.
echo 🔗 默认访问地址: http://localhost:3000
echo.
echo 🧪 测试集成:
echo - 运行: python serena_mt5_integration.py
echo.
echo 📚 更多信息:
echo - 项目文档: https://github.com/oraios/serena
echo - 配置文件: serena\config.json
echo.
pause

"""
Exness MT5连接测试脚本
Exness MT5 Connection Test Script

专门测试Exness服务器的多种连接方式
"""

import MetaTrader5 as mt5
import logging
import time
import socket

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def test_exness_connection():
    """测试Exness连接的多种方式"""
    
    # Exness账户信息
    EXNESS_CONFIG = {
        "login": ********,
        "password": "A4.com123",
        "server_full": "MT5Trial5.exness.com:443",  # 完整服务器地址
        "server_short": "Exness-MT5Trial5",         # 简短服务器名
        "server_domain": "MT5Trial5.exness.com",    # 不带端口的域名
        "path": "C:/Program Files/MetaTrader 5 EXNESS/terminal64.exe"
    }
    
    print("="*80)
    print("Exness MT5连接测试")
    print("="*80)
    
    # 测试DNS解析
    print(f"\n🔍 测试DNS解析...")
    for server_name in [EXNESS_CONFIG["server_domain"], "MT5Trial5.exness.com"]:
        try:
            ip = socket.gethostbyname(server_name)
            print(f"✅ {server_name} -> {ip}")
        except Exception as e:
            print(f"❌ {server_name} -> 解析失败: {e}")
    
    # 测试网络连接
    print(f"\n🌐 测试网络连接...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex(("MT5Trial5.exness.com", 443))
        sock.close()
        if result == 0:
            print(f"✅ MT5Trial5.exness.com:443 网络连接成功")
        else:
            print(f"❌ MT5Trial5.exness.com:443 网络连接失败")
    except Exception as e:
        print(f"❌ 网络连接测试异常: {e}")
    
    # 测试MT5连接的多种方式
    connection_methods = [
        {
            "name": "方法1: 完整服务器地址",
            "server": EXNESS_CONFIG["server_full"],
            "description": "使用 MT5Trial5.exness.com:443"
        },
        {
            "name": "方法2: 域名不带端口",
            "server": EXNESS_CONFIG["server_domain"],
            "description": "使用 MT5Trial5.exness.com"
        },
        {
            "name": "方法3: 简短服务器名",
            "server": EXNESS_CONFIG["server_short"],
            "description": "使用 Exness-MT5Trial5"
        },
        {
            "name": "方法4: 其他可能的名称",
            "server": "MT5Trial5",
            "description": "使用 MT5Trial5"
        }
    ]
    
    successful_methods = []
    
    for method in connection_methods:
        print(f"\n{'='*60}")
        print(f"🔗 {method['name']}")
        print(f"   {method['description']}")
        print(f"{'='*60}")
        
        success = test_single_connection(
            server=method["server"],
            login=EXNESS_CONFIG["login"],
            password=EXNESS_CONFIG["password"],
            path=EXNESS_CONFIG["path"]
        )
        
        if success:
            successful_methods.append(method)
    
    # 总结报告
    print(f"\n{'='*80}")
    print("连接测试总结")
    print(f"{'='*80}")
    
    if successful_methods:
        print(f"✅ 成功的连接方式 ({len(successful_methods)}/{len(connection_methods)}):")
        for method in successful_methods:
            print(f"   ✅ {method['name']}: {method['server']}")
        
        print(f"\n💡 建议使用: {successful_methods[0]['server']}")
        
        # 更新config.py建议
        print(f"\n📝 config.py配置建议:")
        print(f'    "EXNESS": {{')
        print(f'        "server": "{successful_methods[0]["server"]}",')
        print(f'        "login": {EXNESS_CONFIG["login"]},')
        print(f'        "password": "{EXNESS_CONFIG["password"]}",')
        print(f'        "path": "{EXNESS_CONFIG["path"]}",')
        print(f'        "enabled": True')
        print(f'    }}')
        
    else:
        print("❌ 所有连接方式都失败了")
        print("\n🔧 故障排除建议:")
        print("1. 检查账户信息是否正确")
        print("2. 确认MT5终端已安装")
        print("3. 检查网络连接")
        print("4. 尝试在MT5终端中手动登录")
        print("5. 联系Exness客服确认服务器名称")

def test_single_connection(server: str, login: int, password: str, path: str) -> bool:
    """测试单个连接方式"""
    
    print(f"   🔄 测试服务器: {server}")
    
    try:
        # 方法A: 完整参数初始化
        print("      尝试: initialize(完整参数)...")
        if mt5.initialize(path=path, login=login, password=password, server=server):
            account_info = mt5.account_info()
            if account_info:
                print(f"      ✅ 连接成功!")
                print(f"         账户: {account_info.login}")
                print(f"         服务器: {account_info.server}")
                print(f"         公司: {account_info.company}")
                print(f"         余额: {account_info.balance} {account_info.currency}")
                mt5.shutdown()
                return True
            else:
                print(f"      ❌ 无法获取账户信息")
                mt5.shutdown()
        else:
            error = mt5.last_error()
            print(f"      ❌ initialize失败: {error}")
        
        # 方法B: 分步连接
        print("      尝试: initialize(path) + login()...")
        if mt5.initialize(path):
            if mt5.login(login, password, server):
                account_info = mt5.account_info()
                if account_info:
                    print(f"      ✅ 分步连接成功!")
                    print(f"         账户: {account_info.login}")
                    print(f"         服务器: {account_info.server}")
                    mt5.shutdown()
                    return True
                else:
                    print(f"      ❌ 无法获取账户信息")
            else:
                error = mt5.last_error()
                print(f"      ❌ login失败: {error}")
            mt5.shutdown()
        else:
            error = mt5.last_error()
            print(f"      ❌ initialize(path)失败: {error}")
        
        # 方法C: 仅初始化后登录
        print("      尝试: initialize() + login()...")
        if mt5.initialize():
            if mt5.login(login, password, server):
                account_info = mt5.account_info()
                if account_info:
                    print(f"      ✅ 简单连接成功!")
                    print(f"         账户: {account_info.login}")
                    print(f"         服务器: {account_info.server}")
                    mt5.shutdown()
                    return True
                else:
                    print(f"      ❌ 无法获取账户信息")
            else:
                error = mt5.last_error()
                print(f"      ❌ login失败: {error}")
            mt5.shutdown()
        else:
            error = mt5.last_error()
            print(f"      ❌ initialize()失败: {error}")
        
        return False
        
    except Exception as e:
        print(f"      ❌ 连接异常: {e}")
        try:
            mt5.shutdown()
        except:
            pass
        return False

def test_swap_data_retrieval():
    """测试隔夜费数据获取"""
    print(f"\n{'='*60}")
    print("🔍 测试隔夜费数据获取")
    print(f"{'='*60}")
    
    # 使用最可能成功的连接方式
    EXNESS_CONFIG = {
        "login": ********,
        "password": "A4.com123",
        "server": "MT5Trial5.exness.com:443",
        "path": "C:/Program Files/MetaTrader 5 EXNESS/terminal64.exe"
    }
    
    try:
        if mt5.initialize(path=EXNESS_CONFIG["path"], 
                         login=EXNESS_CONFIG["login"], 
                         password=EXNESS_CONFIG["password"], 
                         server=EXNESS_CONFIG["server"]):
            
            print("✅ 连接成功，开始获取隔夜费数据...")
            
            # 测试常见交易品种
            test_symbols = ["EURUSD", "GBPUSD", "USDJPY", "XAUUSD", "USOIL"]
            
            for symbol in test_symbols:
                if mt5.symbol_select(symbol, True):
                    symbol_info = mt5.symbol_info(symbol)
                    if symbol_info:
                        print(f"   {symbol}:")
                        print(f"      Swap Long: {symbol_info.swap_long}")
                        print(f"      Swap Short: {symbol_info.swap_short}")
                        print(f"      Spread: {symbol_info.spread}")
                        print(f"      Currency: {symbol_info.currency_base}/{symbol_info.currency_profit}")
                    else:
                        print(f"   {symbol}: 无法获取信息")
                else:
                    print(f"   {symbol}: 品种不可用")
            
            mt5.shutdown()
            print("\n✅ 隔夜费数据获取测试完成!")
            
        else:
            error = mt5.last_error()
            print(f"❌ 连接失败: {error}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        try:
            mt5.shutdown()
        except:
            pass

if __name__ == "__main__":
    test_exness_connection()
    test_swap_data_retrieval()

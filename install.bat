@echo off
echo ================================================================================
echo MT5多平台隔夜费查询系统 - 安装脚本
echo MT5 Multi-Platform Swap Rate Analysis System - Installation Script
echo ================================================================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python
    echo    请先安装Python 3.8-3.10版本
    echo    下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

python --version
echo ✅ Python环境检查通过

echo.
echo 📦 安装Python依赖包...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 依赖包安装失败
    pause
    exit /b 1
)
echo ✅ 依赖包安装完成

echo.
echo 📁 创建必要目录...
if not exist "results" mkdir results
if not exist "logs" mkdir logs
echo ✅ 目录创建完成

echo.
echo 📋 复制配置文件...
if not exist "config.py" (
    copy "config_example.py" "config.py"
    echo ✅ 已创建config.py配置文件
    echo ⚠️  请编辑config.py文件，填入您的MT5账户信息
) else (
    echo ℹ️  config.py已存在，跳过复制
)

echo.
echo 🔧 设置多MT5实例支持...
python multi_mt5_setup.py
echo ✅ 多实例设置完成

echo.
echo ================================================================================
echo 安装完成! 
echo ================================================================================
echo.
echo 下一步操作:
echo 1. 编辑 config.py 文件，填入您的MT5账户信息
echo 2. 运行 test_connection.py 测试连接
echo 3. 运行 main.py 开始分析
echo.
echo 测试命令: python test_connection.py
echo 运行命令: python main.py
echo.
pause

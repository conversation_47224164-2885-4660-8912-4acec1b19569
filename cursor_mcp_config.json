{"mcpServers": {"serena": {"command": "python", "args": ["C:/Users/<USER>/PycharmProjects/Swap_check/serena/main.py"], "cwd": "C:/Users/<USER>/PycharmProjects/Swap_check/serena", "env": {"PYTHONPATH": "C:/Users/<USER>/PycharmProjects/Swap_check/serena", "SERENA_PORT": "3000", "SERENA_HOST": "localhost", "SERENA_LOG_LEVEL": "info"}}, "mt5-swap-analyzer": {"command": "python", "args": ["C:/Users/<USER>/PycharmProjects/Swap_check/main.py", "--mcp-mode"], "cwd": "C:/Users/<USER>/PycharmProjects/Swap_check", "env": {"PYTHONPATH": "C:/Users/<USER>/PycharmProjects/Swap_check", "MT5_CONFIG_PATH": "C:/Users/<USER>/PycharmProjects/Swap_check/config.py"}}}}
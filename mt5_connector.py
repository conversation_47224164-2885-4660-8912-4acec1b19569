"""
MT5连接器模块
MT5 Connector Module for handling multiple broker connections
"""

import MetaTrader5 as mt5
import logging
import time
import shutil
import os
import sys
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from config import MT5_BROKERS, CONNECTION_CONFIG

@dataclass
class SwapData:
    """隔夜费数据结构"""
    symbol: str
    broker: str
    swap_long: float
    swap_short: float
    spread: float
    digits: int
    point: float
    currency_base: str
    currency_profit: str
    timestamp: float

class MT5Connector:
    """MT5连接器类"""
    
    def __init__(self, broker_name: str, broker_config: dict):
        self.broker_name = broker_name
        self.broker_config = broker_config
        self.is_connected = False
        self.logger = logging.getLogger(f"MT5Connector_{broker_name}")
        
    def connect(self) -> bool:
        """连接到MT5终端"""
        try:
            # 初始化MT5连接
            if not mt5.initialize(
                path=self.broker_config["path"],
                login=self.broker_config["login"],
                server=self.broker_config["server"],
                password=self.broker_config["password"]
            ):
                error = mt5.last_error()
                self.logger.error(f"连接失败 {self.broker_name}: {error}")
                return False
            
            # 验证连接
            account_info = mt5.account_info()
            if account_info is None:
                self.logger.error(f"无法获取账户信息 {self.broker_name}")
                return False
                
            self.is_connected = True
            self.logger.info(f"成功连接到 {self.broker_name} - 账户: {account_info.login}")
            return True
            
        except Exception as e:
            self.logger.error(f"连接异常 {self.broker_name}: {str(e)}")
            return False
    
    def disconnect(self):
        """断开MT5连接"""
        try:
            mt5.shutdown()
            self.is_connected = False
            self.logger.info(f"已断开 {self.broker_name} 连接")
        except Exception as e:
            self.logger.error(f"断开连接异常 {self.broker_name}: {str(e)}")
    
    def get_symbols_list(self, target_symbols: List[str] = None) -> List[str]:
        """获取交易品种列表"""
        if not self.is_connected:
            return []
            
        try:
            if target_symbols:
                # 验证指定品种是否可用
                available_symbols = []
                for symbol in target_symbols:
                    if mt5.symbol_select(symbol, True):
                        available_symbols.append(symbol)
                    else:
                        self.logger.warning(f"品种不可用 {self.broker_name}: {symbol}")
                return available_symbols
            else:
                # 获取所有可用品种
                symbols = mt5.symbols_get()
                if symbols:
                    return [symbol.name for symbol in symbols if symbol.visible]
                return []
                
        except Exception as e:
            self.logger.error(f"获取品种列表异常 {self.broker_name}: {str(e)}")
            return []
    
    def get_swap_data(self, symbol: str) -> Optional[SwapData]:
        """获取指定品种的隔夜费数据"""
        if not self.is_connected:
            return None
            
        try:
            # 确保品种已选择
            if not mt5.symbol_select(symbol, True):
                self.logger.warning(f"无法选择品种 {self.broker_name}: {symbol}")
                return None
            
            # 获取品种信息
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                self.logger.warning(f"无法获取品种信息 {self.broker_name}: {symbol}")
                return None
            
            # 创建SwapData对象
            swap_data = SwapData(
                symbol=symbol,
                broker=self.broker_name,
                swap_long=symbol_info.swap_long,
                swap_short=symbol_info.swap_short,
                spread=symbol_info.spread,
                digits=symbol_info.digits,
                point=symbol_info.point,
                currency_base=symbol_info.currency_base,
                currency_profit=symbol_info.currency_profit,
                timestamp=time.time()
            )
            
            return swap_data
            
        except Exception as e:
            self.logger.error(f"获取隔夜费数据异常 {self.broker_name} {symbol}: {str(e)}")
            return None
    
    def get_all_swap_data(self, target_symbols: List[str] = None) -> List[SwapData]:
        """获取所有品种的隔夜费数据"""
        if not self.is_connected:
            return []
        
        symbols = self.get_symbols_list(target_symbols)
        swap_data_list = []
        
        for symbol in symbols:
            swap_data = self.get_swap_data(symbol)
            if swap_data:
                swap_data_list.append(swap_data)
                
        self.logger.info(f"从 {self.broker_name} 获取了 {len(swap_data_list)} 个品种的数据")
        return swap_data_list

class MT5MultiConnector:
    """多MT5连接管理器"""
    
    def __init__(self):
        self.connectors: Dict[str, MT5Connector] = {}
        self.logger = logging.getLogger("MT5MultiConnector")
        self._setup_multiple_mt5_instances()
    
    def _setup_multiple_mt5_instances(self):
        """设置多个MT5实例支持"""
        # 这里实现复制MetaTrader5包的逻辑以支持多连接
        # 基于Stack Overflow上的解决方案
        pass
    
    def initialize_connectors(self) -> Dict[str, bool]:
        """初始化所有连接器"""
        results = {}
        
        for broker_name, config in MT5_BROKERS.items():
            if not config.get("enabled", True):
                continue
                
            connector = MT5Connector(broker_name, config)
            self.connectors[broker_name] = connector
            
            # 尝试连接
            success = connector.connect()
            results[broker_name] = success
            
            if not success:
                self.logger.error(f"无法连接到 {broker_name}")
            
            # 连接间延迟
            time.sleep(CONNECTION_CONFIG["connection_delay"])
        
        return results
    
    def disconnect_all(self):
        """断开所有连接"""
        for connector in self.connectors.values():
            connector.disconnect()
        self.connectors.clear()
    
    def get_all_swap_data(self, target_symbols: List[str] = None) -> Dict[str, List[SwapData]]:
        """从所有经纪商获取隔夜费数据"""
        all_data = {}
        
        for broker_name, connector in self.connectors.items():
            if connector.is_connected:
                broker_data = connector.get_all_swap_data(target_symbols)
                all_data[broker_name] = broker_data
                self.logger.info(f"从 {broker_name} 获取了 {len(broker_data)} 条数据")
        
        return all_data

"""
MT5多平台隔夜费查询系统配置文件
Configuration file for MT5 Multi-Platform Swap Rate Query System
"""

# MT5经纪商配置
MT5_BROKERS = {
    "ICMarkets": {
        "server": "ICMarkets-Demo",  # 或实盘服务器名
        "login": 12345678,  # 您的账户号
        "password": "your_password",
        "path": "C:/Program Files/ICMarkets - MetaTrader 5/terminal64.exe",
        "enabled": True
    },
    "EXNESS": {
        "server": "Exness-MT5Trial",
        "login": 87654321,
        "password": "your_password", 
        "path": "C:/Program Files/MetaTrader 5 EXNESS/terminal64.exe",
        "enabled": True
    },
    "XM": {
        "server": "XMGlobal-MT5",
        "login": 11111111,
        "password": "your_password",
        "path": "C:/Program Files/XM MT5/terminal64.exe", 
        "enabled": False  # 可以禁用某些经纪商
    },
    "FXPRO": {
        "server": "FxPro-MT5Live",
        "login": 22222222,
        "password": "your_password",
        "path": "C:/Program Files/FxPro - MetaTrader 5/terminal64.exe",
        "enabled": False
    }
}

# 目标交易品种列表 (如果为空则查询所有可用品种)
TARGET_SYMBOLS = [
    # 主要货币对
    "EURUSD", "GBPUSD", "USDJPY", "USDCHF", "AUDUSD", "USDCAD", "NZDUSD",
    "EURJPY", "GBPJPY", "EURGBP", "EURAUD", "EURCHF", "AUDJPY", "GBPAUD",
    
    # 贵金属
    "XAUUSD", "XAGUSD", "XAUEUR", "XAGEUR",
    
    # 原油
    "USOIL", "UKOIL", "NGAS",
    
    # 指数
    "US30", "US500", "NAS100", "GER30", "UK100", "JPN225", "AUS200"
]

# 分析参数
ANALYSIS_CONFIG = {
    "min_swap_difference": 0.5,  # 最小swap差异阈值
    "min_profit_potential": 1.0,  # 最小盈利潜力阈值
    "max_spread_ratio": 0.3,     # 最大点差比率
    "currency_conversion": True,  # 是否进行货币转换
    "base_currency": "USD"       # 基准货币
}

# 输出配置
OUTPUT_CONFIG = {
    "console_output": True,
    "excel_output": True,
    "json_output": True,
    "output_directory": "./results/",
    "filename_prefix": "swap_analysis_"
}

# 连接配置
CONNECTION_CONFIG = {
    "timeout_seconds": 30,
    "retry_attempts": 3,
    "concurrent_connections": True,
    "connection_delay": 2  # 连接间延迟(秒)
}

# 日志配置
LOGGING_CONFIG = {
    "level": "INFO",  # DEBUG, INFO, WARNING, ERROR
    "file_logging": True,
    "console_logging": True,
    "log_directory": "./logs/"
}

"""
MT5连接测试脚本
Test script for MT5 connections and basic functionality
"""

import MetaTrader5 as mt5
import logging
import sys
from config import MT5_BROKERS

def setup_logging():
    """设置简单的日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def test_single_connection(broker_name: str, broker_config: dict) -> bool:
    """测试单个MT5连接"""
    logger = logging.getLogger("TestConnection")
    
    print(f"\n🔗 测试连接到 {broker_name}...")
    
    try:
        # 尝试初始化连接
        if not mt5.initialize(
            path=broker_config["path"],
            login=broker_config["login"],
            server=broker_config["server"],
            password=broker_config["password"]
        ):
            error = mt5.last_error()
            print(f"❌ 连接失败: {error}")
            return False
        
        # 获取账户信息
        account_info = mt5.account_info()
        if account_info is None:
            print(f"❌ 无法获取账户信息")
            mt5.shutdown()
            return False
        
        print(f"✅ 连接成功!")
        print(f"   账户: {account_info.login}")
        print(f"   服务器: {account_info.server}")
        print(f"   公司: {account_info.company}")
        print(f"   余额: {account_info.balance}")
        print(f"   货币: {account_info.currency}")
        
        # 测试获取品种信息
        test_symbols = ["EURUSD", "GBPUSD", "USDJPY"]
        print(f"\n📊 测试获取隔夜费数据:")
        
        for symbol in test_symbols:
            if mt5.symbol_select(symbol, True):
                symbol_info = mt5.symbol_info(symbol)
                if symbol_info:
                    print(f"   {symbol}: swap_long={symbol_info.swap_long}, "
                          f"swap_short={symbol_info.swap_short}, spread={symbol_info.spread}")
                else:
                    print(f"   {symbol}: 无法获取信息")
            else:
                print(f"   {symbol}: 品种不可用")
        
        # 断开连接
        mt5.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ 连接异常: {str(e)}")
        try:
            mt5.shutdown()
        except:
            pass
        return False

def test_mt5_installation():
    """测试MT5安装和Python包"""
    print("🔍 检查MT5 Python包...")
    
    try:
        import MetaTrader5 as mt5
        print(f"✅ MetaTrader5包版本: {mt5.__version__}")
        print(f"   作者: {mt5.__author__}")
        return True
    except ImportError:
        print("❌ MetaTrader5包未安装")
        print("   请运行: pip install MetaTrader5")
        return False
    except Exception as e:
        print(f"❌ MetaTrader5包异常: {str(e)}")
        return False

def main():
    """主测试函数"""
    setup_logging()
    
    print("="*60)
    print("MT5多平台连接测试")
    print("="*60)
    
    # 测试MT5包安装
    if not test_mt5_installation():
        return
    
    # 测试每个配置的经纪商
    successful_connections = 0
    total_brokers = 0
    
    for broker_name, broker_config in MT5_BROKERS.items():
        if not broker_config.get("enabled", True):
            print(f"\n⏭️  跳过 {broker_name} (已禁用)")
            continue
        
        total_brokers += 1
        if test_single_connection(broker_name, broker_config):
            successful_connections += 1
    
    # 总结
    print("\n" + "="*60)
    print("测试总结")
    print("="*60)
    print(f"总经纪商数量: {total_brokers}")
    print(f"成功连接数量: {successful_connections}")
    print(f"失败连接数量: {total_brokers - successful_connections}")
    
    if successful_connections > 0:
        print(f"\n✅ 系统可以正常工作! ({successful_connections}/{total_brokers} 连接成功)")
        print("\n下一步:")
        print("1. 运行 python main.py 开始完整分析")
        print("2. 检查 results/ 目录中的输出文件")
    else:
        print(f"\n❌ 系统无法工作 (0/{total_brokers} 连接成功)")
        print("\n故障排除建议:")
        print("1. 检查config.py中的账户信息是否正确")
        print("2. 确认MT5终端已安装且路径正确")
        print("3. 验证网络连接和服务器状态")
        print("4. 检查账户是否已激活且有足够权限")

if __name__ == "__main__":
    main()

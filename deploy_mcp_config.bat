@echo off
echo ================================================================================
echo 部署Serena MCP配置到Cursor和Augment
echo ================================================================================
echo.

echo 🔍 检测编辑器安装...

REM 检查Cursor
set CURSOR_CONFIG_DIR=%APPDATA%\Cursor\User\globalStorage
if exist "%CURSOR_CONFIG_DIR%" (
    echo ✅ 发现Cursor安装
    set CURSOR_FOUND=1
) else (
    echo ❌ 未找到Cursor安装
    set CURSOR_FOUND=0
)

REM 检查Augment (可能的路径)
set AUGMENT_CONFIG_DIR=%APPDATA%\Augment\User\globalStorage
if exist "%AUGMENT_CONFIG_DIR%" (
    echo ✅ 发现Augment安装
    set AUGMENT_FOUND=1
) else (
    echo ❌ 未找到Augment安装 (检查路径: %AUGMENT_CONFIG_DIR%)
    set AUGMENT_FOUND=0
)

echo.
echo 📋 部署MCP配置文件...

REM 部署到Cursor
if %CURSOR_FOUND%==1 (
    echo 🎯 部署到Cursor...
    
    REM 创建MCP配置目录
    if not exist "%CURSOR_CONFIG_DIR%\mcp" mkdir "%CURSOR_CONFIG_DIR%\mcp"
    
    REM 复制配置文件
    copy "cursor_mcp_config.json" "%CURSOR_CONFIG_DIR%\mcp\servers.json"
    if errorlevel 1 (
        echo ❌ Cursor配置部署失败
    ) else (
        echo ✅ Cursor配置部署成功
        echo    配置文件: %CURSOR_CONFIG_DIR%\mcp\servers.json
    )
) else (
    echo ⏭️  跳过Cursor配置 (未安装)
)

REM 部署到Augment
if %AUGMENT_FOUND%==1 (
    echo 🎯 部署到Augment...
    
    REM 创建MCP配置目录
    if not exist "%AUGMENT_CONFIG_DIR%\mcp" mkdir "%AUGMENT_CONFIG_DIR%\mcp"
    
    REM 复制配置文件
    copy "augment_mcp_config.json" "%AUGMENT_CONFIG_DIR%\mcp\servers.json"
    if errorlevel 1 (
        echo ❌ Augment配置部署失败
    ) else (
        echo ✅ Augment配置部署成功
        echo    配置文件: %AUGMENT_CONFIG_DIR%\mcp\servers.json
    )
) else (
    echo ⏭️  跳过Augment配置 (未安装)
)

echo.
echo 🔧 设置MCP适配器...

REM 复制适配器到serena目录
if exist "serena" (
    copy "serena_mcp_adapter.py" "serena\"
    echo ✅ MCP适配器已复制到serena目录
) else (
    echo ⚠️  serena目录不存在，请确保Serena已正确安装
)

echo.
echo 📝 创建启动脚本...

REM 创建MCP服务器启动脚本
echo @echo off > start_serena_mcp.bat
echo echo 🚀 启动Serena MCP服务器... >> start_serena_mcp.bat
echo cd /d "%CD%\serena" >> start_serena_mcp.bat
echo call serena-env\Scripts\activate.bat >> start_serena_mcp.bat
echo python serena_mcp_adapter.py >> start_serena_mcp.bat
echo pause >> start_serena_mcp.bat

echo ✅ MCP启动脚本已创建: start_serena_mcp.bat

echo.
echo 🧪 创建测试脚本...

REM 创建MCP测试脚本
echo @echo off > test_mcp_connection.bat
echo echo 🧪 测试MCP连接... >> test_mcp_connection.bat
echo echo. >> test_mcp_connection.bat
echo echo 测试Serena服务器连接: >> test_mcp_connection.bat
echo curl -s http://localhost:3000/health ^|^| echo "Serena服务器未运行" >> test_mcp_connection.bat
echo echo. >> test_mcp_connection.bat
echo echo 测试MCP适配器: >> test_mcp_connection.bat
echo python test_mcp_adapter.py >> test_mcp_connection.bat
echo pause >> test_mcp_connection.bat

echo ✅ MCP测试脚本已创建: test_mcp_connection.bat

echo.
echo 📋 创建MCP测试工具...

REM 创建简单的MCP测试脚本
echo import json > test_mcp_adapter.py
echo import subprocess >> test_mcp_adapter.py
echo import sys >> test_mcp_adapter.py
echo. >> test_mcp_adapter.py
echo def test_mcp_adapter(): >> test_mcp_adapter.py
echo     """测试MCP适配器""" >> test_mcp_adapter.py
echo     try: >> test_mcp_adapter.py
echo         # 测试初始化请求 >> test_mcp_adapter.py
echo         test_request = { >> test_mcp_adapter.py
echo             "jsonrpc": "2.0", >> test_mcp_adapter.py
echo             "id": 1, >> test_mcp_adapter.py
echo             "method": "initialize", >> test_mcp_adapter.py
echo             "params": {} >> test_mcp_adapter.py
echo         } >> test_mcp_adapter.py
echo         print("✅ MCP适配器测试脚本已创建") >> test_mcp_adapter.py
echo         print("📋 测试请求:", json.dumps(test_request, indent=2)) >> test_mcp_adapter.py
echo     except Exception as e: >> test_mcp_adapter.py
echo         print(f"❌ 测试失败: {e}") >> test_mcp_adapter.py
echo. >> test_mcp_adapter.py
echo if __name__ == "__main__": >> test_mcp_adapter.py
echo     test_mcp_adapter() >> test_mcp_adapter.py

echo ✅ MCP测试工具已创建: test_mcp_adapter.py

echo.
echo ================================================================================
echo 🎉 MCP配置部署完成!
echo ================================================================================
echo.
echo 📁 配置文件位置:
if %CURSOR_FOUND%==1 (
    echo    Cursor: %CURSOR_CONFIG_DIR%\mcp\servers.json
)
if %AUGMENT_FOUND%==1 (
    echo    Augment: %AUGMENT_CONFIG_DIR%\mcp\servers.json
)
echo.
echo 🚀 启动步骤:
echo 1. 启动Serena服务器:
echo    cd serena
echo    start_serena.bat
echo.
echo 2. 启动MCP适配器:
echo    start_serena_mcp.bat
echo.
echo 3. 在Cursor/Augment中重启或重新加载MCP服务器
echo.
echo 🧪 测试连接:
echo    test_mcp_connection.bat
echo.
echo 📚 使用说明:
echo - 在Cursor中: 使用 @serena 调用MCP服务器
echo - 在Augment中: MCP服务器会自动加载到上下文中
echo.
echo 🔧 如果遇到问题:
echo 1. 检查Serena服务器是否运行 (http://localhost:3000)
echo 2. 检查MCP适配器是否正常启动
echo 3. 查看编辑器的MCP服务器日志
echo 4. 确保Python环境和依赖正确安装
echo.
pause

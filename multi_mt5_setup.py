"""
多MT5实例设置工具
Multi-MT5 Instance Setup Utility
基于Stack Overflow解决方案实现多连接支持
"""

import os
import shutil
import sys
import logging
from pathlib import Path

class MultiMT5Setup:
    """多MT5实例设置工具"""
    
    def __init__(self):
        self.logger = logging.getLogger("MultiMT5Setup")
        self.python_site_packages = self._get_site_packages_path()
        self.mt5_package_path = os.path.join(self.python_site_packages, "MetaTrader5")
    
    def _get_site_packages_path(self) -> str:
        """获取Python site-packages路径"""
        for path in sys.path:
            if "site-packages" in path:
                return path
        
        # 备用方法
        import site
        return site.getsitepackages()[0]
    
    def setup_multiple_instances(self, broker_names: list) -> bool:
        """为多个经纪商设置独立的MT5包实例"""
        try:
            if not os.path.exists(self.mt5_package_path):
                self.logger.error("未找到MetaTrader5包，请先安装: pip install MetaTrader5")
                return False
            
            success_count = 0
            for broker_name in broker_names:
                if self._create_broker_instance(broker_name):
                    success_count += 1
            
            self.logger.info(f"成功创建 {success_count}/{len(broker_names)} 个MT5实例")
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"设置多实例失败: {str(e)}")
            return False
    
    def _create_broker_instance(self, broker_name: str) -> bool:
        """为指定经纪商创建MT5包实例"""
        try:
            # 创建经纪商专用的包名
            broker_package_name = f"MetaTrader5_{broker_name}"
            broker_package_path = os.path.join(self.python_site_packages, broker_package_name)
            
            # 如果已存在则跳过
            if os.path.exists(broker_package_path):
                self.logger.info(f"MT5实例已存在: {broker_package_name}")
                return True
            
            # 复制原始MetaTrader5包
            shutil.copytree(self.mt5_package_path, broker_package_path)
            
            # 修改__init__.py文件以避免冲突
            self._modify_init_file(broker_package_path, broker_name)
            
            self.logger.info(f"成功创建MT5实例: {broker_package_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建{broker_name}实例失败: {str(e)}")
            return False
    
    def _modify_init_file(self, package_path: str, broker_name: str):
        """修改__init__.py文件以支持独立实例"""
        init_file = os.path.join(package_path, "__init__.py")
        
        if not os.path.exists(init_file):
            return
        
        try:
            # 读取原始内容
            with open(init_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 添加经纪商标识注释
            modified_content = f"""# Modified for {broker_name} - {broker_name} specific instance
{content}
"""
            
            # 写回修改后的内容
            with open(init_file, 'w', encoding='utf-8') as f:
                f.write(modified_content)
                
        except Exception as e:
            self.logger.warning(f"修改{broker_name}的__init__.py失败: {str(e)}")
    
    def cleanup_instances(self, broker_names: list = None):
        """清理MT5实例"""
        try:
            if broker_names is None:
                # 查找所有MT5实例
                broker_names = []
                for item in os.listdir(self.python_site_packages):
                    if item.startswith("MetaTrader5_") and os.path.isdir(
                        os.path.join(self.python_site_packages, item)
                    ):
                        broker_name = item.replace("MetaTrader5_", "")
                        broker_names.append(broker_name)
            
            removed_count = 0
            for broker_name in broker_names:
                broker_package_name = f"MetaTrader5_{broker_name}"
                broker_package_path = os.path.join(self.python_site_packages, broker_package_name)
                
                if os.path.exists(broker_package_path):
                    shutil.rmtree(broker_package_path)
                    removed_count += 1
                    self.logger.info(f"已删除MT5实例: {broker_package_name}")
            
            self.logger.info(f"清理完成，删除了 {removed_count} 个实例")
            
        except Exception as e:
            self.logger.error(f"清理实例失败: {str(e)}")
    
    def check_setup_status(self, broker_names: list) -> dict:
        """检查多实例设置状态"""
        status = {}
        
        for broker_name in broker_names:
            broker_package_name = f"MetaTrader5_{broker_name}"
            broker_package_path = os.path.join(self.python_site_packages, broker_package_name)
            status[broker_name] = os.path.exists(broker_package_path)
        
        return status

def setup_multi_mt5_for_brokers(broker_names: list) -> bool:
    """为指定经纪商设置多MT5实例的便捷函数"""
    setup_tool = MultiMT5Setup()
    return setup_tool.setup_multiple_instances(broker_names)

if __name__ == "__main__":
    # 测试脚本
    logging.basicConfig(level=logging.INFO)
    
    # 示例经纪商列表
    test_brokers = ["ICMarkets", "EXNESS", "XM", "FXPRO"]
    
    setup_tool = MultiMT5Setup()
    
    print("检查当前状态...")
    status = setup_tool.check_setup_status(test_brokers)
    for broker, exists in status.items():
        print(f"  {broker}: {'已设置' if exists else '未设置'}")
    
    print("\n设置多实例...")
    success = setup_tool.setup_multiple_instances(test_brokers)
    
    if success:
        print("✅ 多实例设置完成")
    else:
        print("❌ 多实例设置失败")

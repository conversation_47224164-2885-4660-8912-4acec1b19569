# MT5多平台隔夜费查询分析系统

## 项目简介

这是一个用于查询多个MetaTrader 5平台隔夜费率并识别套利机会的Python系统。系统能够同时连接多个MT5经纪商，收集隔夜费数据，并通过智能分析识别潜在的套利机会。

## 主要功能

- 🔗 **多平台连接**: 同时连接多个MT5经纪商平台
- 📊 **数据收集**: 自动收集隔夜费率(swap rates)数据
- 🔍 **套利分析**: 智能识别跨平台套利机会
- 📋 **多格式报告**: 支持控制台、Excel、JSON格式输出
- ⚡ **并发处理**: 高效的并发数据收集
- 🛡️ **风险评估**: 自动评估套利机会的风险等级

## 技术可行性

### ✅ 已验证的技术方案

1. **MT5 Python API**: 使用官方MetaTrader5包
2. **多连接支持**: 基于Stack Overflow验证的解决方案
3. **数据完整性**: 获取包括swap_long、swap_short在内的完整数据
4. **实时更新**: 数据反映当前市场状况

### 🔧 解决的技术挑战

- **多连接限制**: 通过复制MT5包实现多实例连接
- **服务器名称连接**: 支持使用服务器名而非IP地址
- **数据同步**: 确保多平台数据的时间一致性

## 安装指南

### 1. 环境要求

- Python 3.8-3.10
- Windows操作系统 (MT5要求)
- 已安装的MetaTrader 5终端

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置设置

编辑 `config.py` 文件，配置您的MT5经纪商信息：

```python
MT5_BROKERS = {
    "ICMarkets": {
        "server": "ICMarkets-Demo",
        "login": 12345678,
        "password": "your_password",
        "path": "C:/Program Files/ICMarkets - MetaTrader 5/terminal64.exe",
        "enabled": True
    },
    # 添加更多经纪商...
}
```

### 4. 设置多实例支持

```bash
python multi_mt5_setup.py
```

## 使用方法

### 基本使用

```bash
python main.py
```

### 配置选项

1. **目标品种**: 在`config.py`中设置`TARGET_SYMBOLS`
2. **分析参数**: 调整`ANALYSIS_CONFIG`中的阈值
3. **输出格式**: 配置`OUTPUT_CONFIG`选择输出格式

## 输出示例

### 控制台输出
```
================================================================================
MT5多平台隔夜费套利机会分析报告
================================================================================

📊 分析摘要:
   总套利机会数量: 15
   高盈利潜力 (>5): 3
   中等盈利潜力 (2-5): 7
   低盈利潜力 (≤2): 5
   平均盈利潜力: 3.45
   最大盈利潜力: 8.92

🎯 前10个最佳套利机会:
--------------------------------------------------------
品种       做多经纪商    做空经纪商    隔夜费差   净盈利潜力   风险等级   建议
--------------------------------------------------------
EURUSD     ICMarkets    EXNESS       2.50      8.92        低风险     强烈推荐
GBPUSD     XM          ICMarkets     1.80      6.45        低风险     强烈推荐
...
```

### Excel报告

生成包含以下工作表的Excel文件：
- **套利机会**: 详细的套利机会列表
- **分析摘要**: 统计摘要信息
- **原始数据**: 所有收集的隔夜费数据

## 项目结构

```
Swap_check/
├── main.py                 # 主程序入口
├── config.py              # 配置文件
├── mt5_connector.py       # MT5连接器模块
├── swap_analyzer.py       # 隔夜费分析模块
├── output_manager.py      # 输出管理模块
├── multi_mt5_setup.py     # 多实例设置工具
├── requirements.txt       # 依赖包列表
├── README.md             # 项目说明
├── results/              # 输出结果目录
└── logs/                 # 日志文件目录
```

## 核心算法

### 套利机会识别

系统通过以下步骤识别套利机会：

1. **数据收集**: 从多个平台收集隔夜费数据
2. **交叉比较**: 比较同一品种在不同平台的费率
3. **盈利计算**: 计算潜在的日收益
4. **风险评估**: 评估点差成本和市场风险
5. **机会排序**: 按净盈利潜力排序

### 风险评估模型

- **低风险**: 盈利潜力 > 点差成本 × 5
- **中等风险**: 盈利潜力 > 点差成本 × 2
- **高风险**: 其他情况

## 注意事项

### ⚠️ 重要提醒

1. **模拟测试**: 建议先在模拟账户上测试
2. **实时监控**: 隔夜费率可能随时变化
3. **风险管理**: 注意资金管理和风险控制
4. **合规性**: 确保符合各经纪商的交易规则

### 🔒 安全建议

- 不要在代码中硬编码密码
- 使用环境变量存储敏感信息
- 定期更新依赖包
- 监控异常连接活动

## 故障排除

### 常见问题

1. **连接失败**
   - 检查MT5终端是否运行
   - 验证账户信息是否正确
   - 确认服务器名称无误

2. **数据不完整**
   - 检查品种是否在平台上可用
   - 验证市场是否开放
   - 确认账户权限

3. **多连接问题**
   - 运行多实例设置工具
   - 检查防火墙设置
   - 验证MT5安装路径

## 更新日志

### v1.0.0 (2025-01-29)
- 初始版本发布
- 支持多平台连接
- 实现套利机会分析
- 添加多格式报告输出

## 许可证

本项目仅供学习和研究使用。使用本系统进行实际交易时，请自行承担风险。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建GitHub Issue
- 发送邮件至项目维护者

---

**免责声明**: 本系统仅供教育和研究目的。外汇交易存在高风险，可能导致资金损失。请在充分了解风险的情况下使用本系统。

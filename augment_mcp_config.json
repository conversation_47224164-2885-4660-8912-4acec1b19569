{"version": "1.0", "servers": {"serena-mcp": {"name": "Serena MCP Server", "description": "Serena AI助手，集成MT5隔夜费分析功能", "type": "stdio", "command": "python", "args": ["-m", "serena.mcp_server"], "cwd": "C:/Users/<USER>/PycharmProjects/Swap_check/serena", "env": {"PYTHONPATH": "C:/Users/<USER>/PycharmProjects/Swap_check/serena", "VIRTUAL_ENV": "C:/Users/<USER>/PycharmProjects/Swap_check/serena/serena-env", "PATH": "C:/Users/<USER>/PycharmProjects/Swap_check/serena/serena-env/Scripts;${PATH}"}, "capabilities": ["tools", "resources", "prompts"], "tools": [{"name": "analyze_swap_rates", "description": "分析MT5隔夜费数据并提供套利建议"}, {"name": "get_market_data", "description": "获取实时市场数据和隔夜费信息"}, {"name": "generate_report", "description": "生成详细的隔夜费分析报告"}]}, "mt5-data-provider": {"name": "MT5 Data Provider", "description": "MT5数据提供服务，专门处理隔夜费查询", "type": "http", "baseUrl": "http://localhost:3000", "endpoints": {"swap_data": "/api/swap-rates", "symbols": "/api/symbols", "analysis": "/api/analysis"}, "auth": {"type": "none"}, "timeout": 30000}}, "workflows": {"swap_analysis": {"name": "隔夜费套利分析工作流", "description": "完整的MT5隔夜费分析和套利机会识别流程", "steps": [{"server": "mt5-data-provider", "action": "fetch_swap_data", "description": "获取所有经纪商的隔夜费数据"}, {"server": "serena-mcp", "action": "analyze_opportunities", "description": "使用AI分析套利机会"}, {"server": "serena-mcp", "action": "generate_recommendations", "description": "生成交易建议和风险评估"}]}}}
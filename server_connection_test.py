"""
MT5服务器名称连接测试脚本
Server Name Connection Test Script for MT5

专门测试MT5 Python API是否能够使用经纪商提供的服务器名称进行连接
Specifically tests whether MT5 Python API can connect using broker-provided server names
"""

import MetaTrader5 as mt5
import logging
import time
import socket
import subprocess
import sys

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def test_dns_resolution(server_name: str) -> bool:
    """测试DNS解析"""
    logger = logging.getLogger("DNSTest")
    
    print(f"\n🔍 测试DNS解析: {server_name}")
    
    try:
        # 尝试DNS解析
        ip_address = socket.gethostbyname(server_name)
        print(f"✅ DNS解析成功: {server_name} -> {ip_address}")
        return True
    except socket.gaierror as e:
        print(f"❌ DNS解析失败: {server_name} - {str(e)}")
        return False
    except Exception as e:
        print(f"❌ DNS解析异常: {server_name} - {str(e)}")
        return False

def test_network_connectivity(server_name: str, port: int = 443) -> bool:
    """测试网络连接"""
    print(f"\n🌐 测试网络连接: {server_name}:{port}")
    
    try:
        # 创建socket连接测试
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        
        result = sock.connect_ex((server_name, port))
        sock.close()
        
        if result == 0:
            print(f"✅ 网络连接成功: {server_name}:{port}")
            return True
        else:
            print(f"❌ 网络连接失败: {server_name}:{port} - 错误代码: {result}")
            return False
            
    except Exception as e:
        print(f"❌ 网络连接异常: {server_name}:{port} - {str(e)}")
        return False

def test_ping(server_name: str) -> bool:
    """测试ping连通性"""
    print(f"\n🏓 测试Ping连通性: {server_name}")
    
    try:
        # Windows ping命令
        result = subprocess.run(
            ["ping", "-n", "4", server_name],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            print(f"✅ Ping成功: {server_name}")
            # 提取ping统计信息
            lines = result.stdout.split('\n')
            for line in lines:
                if 'Average' in line or '平均' in line:
                    print(f"   {line.strip()}")
            return True
        else:
            print(f"❌ Ping失败: {server_name}")
            print(f"   错误输出: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ Ping超时: {server_name}")
        return False
    except Exception as e:
        print(f"❌ Ping异常: {server_name} - {str(e)}")
        return False

def test_mt5_server_connection(server_name: str, login: int = None, password: str = None, path: str = None) -> dict:
    """测试MT5服务器连接"""
    print(f"\n🔗 测试MT5服务器连接: {server_name}")
    
    result = {
        "server_name": server_name,
        "connection_success": False,
        "error_code": None,
        "error_message": None,
        "account_info": None,
        "connection_method": None
    }
    
    try:
        # 方法1: 仅使用服务器名称初始化
        print("   尝试方法1: 仅服务器名称初始化...")
        if mt5.initialize():
            if login and password:
                login_result = mt5.login(login, password, server_name)
                if login_result:
                    result["connection_success"] = True
                    result["connection_method"] = "initialize() + login()"
                    result["account_info"] = mt5.account_info()._asdict() if mt5.account_info() else None
                    print("   ✅ 方法1成功: initialize() + login()")
                else:
                    error = mt5.last_error()
                    result["error_code"] = error[0] if error else None
                    result["error_message"] = error[1] if error else None
                    print(f"   ❌ 方法1失败: login() - {error}")
            else:
                print("   ⚠️  方法1跳过: 缺少登录信息")
            
            mt5.shutdown()
        
        # 方法2: 使用完整参数初始化
        if not result["connection_success"] and login and password:
            print("   尝试方法2: 完整参数初始化...")
            
            init_params = {}
            if path:
                init_params["path"] = path
            if login:
                init_params["login"] = login
            if password:
                init_params["password"] = password
            if server_name:
                init_params["server"] = server_name
            
            if mt5.initialize(**init_params):
                result["connection_success"] = True
                result["connection_method"] = "initialize(path, login, password, server)"
                result["account_info"] = mt5.account_info()._asdict() if mt5.account_info() else None
                print("   ✅ 方法2成功: initialize(完整参数)")
                mt5.shutdown()
            else:
                error = mt5.last_error()
                result["error_code"] = error[0] if error else None
                result["error_message"] = error[1] if error else None
                print(f"   ❌ 方法2失败: initialize() - {error}")
        
        # 方法3: 仅路径初始化后登录
        if not result["connection_success"] and path and login and password:
            print("   尝试方法3: 路径初始化后登录...")
            
            if mt5.initialize(path):
                login_result = mt5.login(login, password, server_name)
                if login_result:
                    result["connection_success"] = True
                    result["connection_method"] = "initialize(path) + login()"
                    result["account_info"] = mt5.account_info()._asdict() if mt5.account_info() else None
                    print("   ✅ 方法3成功: initialize(path) + login()")
                else:
                    error = mt5.last_error()
                    result["error_code"] = error[0] if error else None
                    result["error_message"] = error[1] if error else None
                    print(f"   ❌ 方法3失败: login() - {error}")
                
                mt5.shutdown()
            else:
                error = mt5.last_error()
                print(f"   ❌ 方法3失败: initialize(path) - {error}")
        
    except Exception as e:
        result["error_message"] = str(e)
        print(f"   ❌ MT5连接异常: {str(e)}")
        try:
            mt5.shutdown()
        except:
            pass
    
    return result

def main():
    """主测试函数"""
    setup_logging()
    
    print("="*80)
    print("MT5服务器名称连接机制测试")
    print("MT5 Server Name Connection Mechanism Test")
    print("="*80)
    
    # 测试常见的MT5服务器名称
    test_servers = [
        # ICMarkets
        {
            "name": "ICMarkets-Demo",
            "description": "ICMarkets 模拟服务器",
            "login": None,  # 需要真实账户信息
            "password": None,
            "path": "C:/Program Files/ICMarkets - MetaTrader 5/terminal64.exe"
        },
        # EXNESS
        {
            "name": "mt5trial5.exness.com:443",
            "description": "EXNESS 模拟服务器",
            "login": 76905307,
            "password": 'A4.com123',
            "path": "C:/Program Files/MetaTrader 5 EXNESS/terminal64.exe"
        },
        # XM
        {
            "name": "XMGlobal-Demo",
            "description": "XM 模拟服务器",
            "login": None,
            "password": None,
            "path": "C:/Program Files/XM MT5/terminal64.exe"
        },
        # 通用测试
        {
            "name": "demo.metaquotes.net",
            "description": "MetaQuotes 演示服务器",
            "login": None,
            "password": None,
            "path": "C:/Program Files/MetaTrader 5/terminal64.exe"
        }
    ]
    
    results = []
    
    for server in test_servers:
        print(f"\n{'='*60}")
        print(f"测试服务器: {server['name']} ({server['description']})")
        print(f"{'='*60}")
        
        server_result = {
            "server": server,
            "dns_resolution": False,
            "network_connectivity": False,
            "ping_success": False,
            "mt5_connection": None
        }
        
        # 1. DNS解析测试
        server_result["dns_resolution"] = test_dns_resolution(server["name"])
        
        # 2. 网络连接测试
        if server_result["dns_resolution"]:
            server_result["network_connectivity"] = test_network_connectivity(server["name"])
            server_result["ping_success"] = test_ping(server["name"])
        
        # 3. MT5连接测试
        server_result["mt5_connection"] = test_mt5_server_connection(
            server["name"],
            server.get("login"),
            server.get("password"),
            server.get("path")
        )
        
        results.append(server_result)
    
    # 总结报告
    print(f"\n{'='*80}")
    print("测试总结报告")
    print(f"{'='*80}")
    
    for i, result in enumerate(results, 1):
        server = result["server"]
        print(f"\n{i}. {server['name']} ({server['description']})")
        print(f"   DNS解析: {'✅' if result['dns_resolution'] else '❌'}")
        print(f"   网络连接: {'✅' if result['network_connectivity'] else '❌'}")
        print(f"   Ping连通: {'✅' if result['ping_success'] else '❌'}")
        
        mt5_conn = result["mt5_connection"]
        if mt5_conn:
            if mt5_conn["connection_success"]:
                print(f"   MT5连接: ✅ ({mt5_conn['connection_method']})")
                if mt5_conn["account_info"]:
                    acc_info = mt5_conn["account_info"]
                    print(f"   账户信息: {acc_info.get('login', 'N/A')} @ {acc_info.get('server', 'N/A')}")
            else:
                error_msg = mt5_conn.get("error_message", "未知错误")
                error_code = mt5_conn.get("error_code", "N/A")
                print(f"   MT5连接: ❌ (错误: {error_code} - {error_msg})")
        else:
            print(f"   MT5连接: ⏭️  (跳过)")
    
    # 技术可行性结论
    print(f"\n{'='*80}")
    print("技术可行性结论")
    print(f"{'='*80}")
    
    dns_success_count = sum(1 for r in results if r["dns_resolution"])
    network_success_count = sum(1 for r in results if r["network_connectivity"])
    
    print(f"DNS解析成功率: {dns_success_count}/{len(results)} ({dns_success_count/len(results)*100:.1f}%)")
    print(f"网络连接成功率: {network_success_count}/{len(results)} ({network_success_count/len(results)*100:.1f}%)")
    
    if dns_success_count > 0:
        print("\n✅ 结论: MT5服务器名称可以通过DNS解析")
        print("   - MT5客户端使用标准DNS解析将服务器名称转换为IP地址")
        print("   - Python程序可以使用相同的机制连接到MT5服务器")
        print("   - 不需要额外的网络配置或特殊设置")
    else:
        print("\n❌ 结论: 无法解析测试的服务器名称")
        print("   - 可能需要有效的MT5账户才能访问服务器")
        print("   - 或者测试的服务器名称不正确")
    
    print(f"\n💡 建议:")
    print("1. 使用真实的MT5账户信息进行完整测试")
    print("2. 确保MT5终端已正确安装")
    print("3. 检查防火墙和网络设置")
    print("4. 联系经纪商获取正确的服务器名称")

if __name__ == "__main__":
    main()

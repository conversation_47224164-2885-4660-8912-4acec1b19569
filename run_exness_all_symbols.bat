@echo off
echo ================================================================================
echo Exness所有标的隔夜费查询
echo ================================================================================
echo.

echo 🔄 激活虚拟环境...
if exist "venv\Scripts\activate.bat" (
    call venv\Scripts\activate.bat
    echo ✅ 虚拟环境已激活
) else (
    echo ⚠️  虚拟环境不存在，使用系统Python
)

echo.
echo 📊 开始获取所有标的的隔夜费数据...
echo ⏰ 这可能需要几分钟时间，请耐心等待...
echo.

python exness_all_symbols_swap.py

echo.
echo 📋 查看结果文件...
if exist "results" (
    echo 📁 results目录内容:
    dir results\*.xlsx /b 2>nul
    dir results\*.csv /b 2>nul
)

echo.
echo 🎉 任务完成! 请查看results目录中的文件
pause

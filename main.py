"""
MT5多平台隔夜费查询系统主程序
Main program for MT5 Multi-Platform Swap Rate Query System
"""

import logging
import sys
import os
from datetime import datetime
from mt5_connector import MT5MultiConnector
from swap_analyzer import SwapAnalyzer
from output_manager import OutputManager
from config import TARGET_SYMBOLS, LOGGING_CONFIG

def setup_logging():
    """设置日志系统"""
    # 确保日志目录存在
    if not os.path.exists(LOGGING_CONFIG["log_directory"]):
        os.makedirs(LOGGING_CONFIG["log_directory"])
    
    # 配置日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 设置日志级别
    log_level = getattr(logging, LOGGING_CONFIG["level"].upper())
    
    # 配置根日志器
    logging.basicConfig(
        level=log_level,
        format=log_format,
        handlers=[]
    )
    
    # 添加文件处理器
    if LOGGING_CONFIG["file_logging"]:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = f"swap_analysis_{timestamp}.log"
        log_filepath = os.path.join(LOGGING_CONFIG["log_directory"], log_filename)
        
        file_handler = logging.FileHandler(log_filepath, encoding='utf-8')
        file_handler.setFormatter(logging.Formatter(log_format))
        logging.getLogger().addHandler(file_handler)
    
    # 添加控制台处理器
    if LOGGING_CONFIG["console_logging"]:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(logging.Formatter(log_format))
        logging.getLogger().addHandler(console_handler)

def print_banner():
    """打印程序横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                MT5多平台隔夜费查询分析系统                    ║
    ║            MT5 Multi-Platform Swap Rate Analysis             ║
    ║                                                              ║
    ║  功能: 查询多个MT5平台的隔夜费率并识别套利机会                ║
    ║  版本: 1.0.0                                                 ║
    ║  作者: AI Assistant                                          ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def main():
    """主函数"""
    # 设置日志
    setup_logging()
    logger = logging.getLogger("MainProgram")
    
    # 打印横幅
    print_banner()
    
    try:
        logger.info("程序启动")
        
        # 初始化组件
        multi_connector = MT5MultiConnector()
        analyzer = SwapAnalyzer()
        output_manager = OutputManager()
        
        # 第一阶段：连接到所有MT5平台
        logger.info("开始连接MT5平台...")
        connection_results = multi_connector.initialize_connectors()
        output_manager.print_connection_status(connection_results)
        
        # 检查是否有成功的连接
        successful_connections = sum(connection_results.values())
        if successful_connections == 0:
            logger.error("没有成功连接到任何MT5平台，程序退出")
            print("\n❌ 错误: 无法连接到任何MT5平台")
            print("请检查:")
            print("1. MT5终端是否已安装并正确配置")
            print("2. 账户登录信息是否正确")
            print("3. 服务器名称是否正确")
            print("4. 网络连接是否正常")
            return
        
        logger.info(f"成功连接到 {successful_connections} 个MT5平台")
        
        # 第二阶段：收集隔夜费数据
        logger.info("开始收集隔夜费数据...")
        print(f"\n📊 正在从 {successful_connections} 个平台收集数据...")
        
        # 使用配置中的目标品种，如果为空则查询所有品种
        target_symbols = TARGET_SYMBOLS if TARGET_SYMBOLS else None
        all_swap_data = multi_connector.get_all_swap_data(target_symbols)
        
        output_manager.print_data_collection_summary(all_swap_data)
        
        # 检查是否收集到数据
        total_data_count = sum(len(data) for data in all_swap_data.values())
        if total_data_count == 0:
            logger.warning("未收集到任何隔夜费数据")
            print("\n⚠️  警告: 未收集到任何数据")
            return
        
        # 第三阶段：分析套利机会
        logger.info("开始分析套利机会...")
        print(f"\n🔍 正在分析 {total_data_count} 条数据记录...")
        
        # 创建数据框
        df = analyzer.create_dataframe(all_swap_data)
        
        # 寻找套利机会
        opportunities = analyzer.find_arbitrage_opportunities(df)
        
        # 生成摘要报告
        summary = analyzer.create_summary_report(opportunities)
        
        # 第四阶段：输出结果
        logger.info("生成分析报告...")
        print(f"\n📋 生成分析报告...")
        
        # 保存所有格式的报告
        saved_files = output_manager.save_all_reports(opportunities, summary, all_swap_data)
        
        # 显示保存的文件
        if saved_files:
            print(f"\n💾 报告已保存:")
            for format_type, filepath in saved_files.items():
                print(f"   📄 {format_type.upper()}: {filepath}")
        
        # 程序完成
        logger.info("程序执行完成")
        print(f"\n✅ 分析完成! 发现 {len(opportunities)} 个潜在套利机会")
        
        if opportunities:
            best_opportunity = opportunities[0]
            print(f"🎯 最佳机会: {best_opportunity.symbol} "
                  f"({best_opportunity.broker_long} vs {best_opportunity.broker_short}) "
                  f"净盈利潜力: {best_opportunity.net_profit_potential:.2f}")
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
        print("\n\n⏹️  程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行异常: {str(e)}", exc_info=True)
        print(f"\n❌ 程序执行出错: {str(e)}")
    finally:
        # 清理资源
        try:
            multi_connector.disconnect_all()
            logger.info("已断开所有MT5连接")
        except:
            pass
        
        print("\n👋 程序结束")

if __name__ == "__main__":
    main()

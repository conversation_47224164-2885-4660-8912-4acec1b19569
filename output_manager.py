"""
输出管理模块
Output Manager Module for generating reports and saving results
"""

import pandas as pd
import json
import os
import logging
from datetime import datetime
from typing import List, Dict
from dataclasses import asdict
from swap_analyzer import ArbitrageOpportunity
from config import OUTPUT_CONFIG

class OutputManager:
    """输出管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger("OutputManager")
        self.output_config = OUTPUT_CONFIG
        self._ensure_directories()
    
    def _ensure_directories(self):
        """确保输出目录存在"""
        directories = [
            self.output_config["output_directory"],
            "logs/"
        ]
        
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                self.logger.info(f"创建目录: {directory}")
    
    def generate_timestamp(self) -> str:
        """生成时间戳"""
        return datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def print_console_report(self, opportunities: List[ArbitrageOpportunity], 
                           summary: Dict):
        """打印控制台报告"""
        if not self.output_config["console_output"]:
            return
        
        print("\n" + "="*80)
        print("MT5多平台隔夜费套利机会分析报告")
        print("="*80)
        
        # 摘要信息
        print(f"\n📊 分析摘要:")
        print(f"   总套利机会数量: {summary['total_opportunities']}")
        if summary['total_opportunities'] > 0:
            print(f"   高盈利潜力 (>5): {summary['high_potential_count']}")
            print(f"   中等盈利潜力 (2-5): {summary['medium_potential_count']}")
            print(f"   低盈利潜力 (≤2): {summary['low_potential_count']}")
            print(f"   平均盈利潜力: {summary['avg_profit_potential']:.2f}")
            print(f"   最大盈利潜力: {summary['max_profit_potential']:.2f}")
        
        # 前10个最佳机会
        if opportunities:
            print(f"\n🎯 前10个最佳套利机会:")
            print("-" * 120)
            print(f"{'品种':<10} {'做多经纪商':<12} {'做空经纪商':<12} {'隔夜费差':<10} {'净盈利潜力':<12} {'风险等级':<10} {'建议':<10}")
            print("-" * 120)
            
            for i, opp in enumerate(opportunities[:10]):
                print(f"{opp.symbol:<10} {opp.broker_long:<12} {opp.broker_short:<12} "
                      f"{opp.swap_difference:<10.2f} {opp.net_profit_potential:<12.2f} "
                      f"{opp.risk_level:<10} {opp.recommendation:<10}")
        
        print("\n" + "="*80)
    
    def save_excel_report(self, opportunities: List[ArbitrageOpportunity], 
                         summary: Dict, all_swap_data: Dict) -> str:
        """保存Excel报告"""
        if not self.output_config["excel_output"]:
            return ""
        
        timestamp = self.generate_timestamp()
        filename = f"{self.output_config['filename_prefix']}{timestamp}.xlsx"
        filepath = os.path.join(self.output_config["output_directory"], filename)
        
        try:
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                # 套利机会表
                if opportunities:
                    opp_df = pd.DataFrame([asdict(opp) for opp in opportunities])
                    opp_df.to_excel(writer, sheet_name='套利机会', index=False)
                
                # 摘要表
                summary_df = pd.DataFrame([summary])
                summary_df.to_excel(writer, sheet_name='分析摘要', index=False)
                
                # 原始数据表
                raw_data_rows = []
                for broker, swap_data_list in all_swap_data.items():
                    for swap_data in swap_data_list:
                        raw_data_rows.append(asdict(swap_data))
                
                if raw_data_rows:
                    raw_df = pd.DataFrame(raw_data_rows)
                    raw_df.to_excel(writer, sheet_name='原始数据', index=False)
            
            self.logger.info(f"Excel报告已保存: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"保存Excel报告失败: {str(e)}")
            return ""
    
    def save_json_report(self, opportunities: List[ArbitrageOpportunity], 
                        summary: Dict, all_swap_data: Dict) -> str:
        """保存JSON报告"""
        if not self.output_config["json_output"]:
            return ""
        
        timestamp = self.generate_timestamp()
        filename = f"{self.output_config['filename_prefix']}{timestamp}.json"
        filepath = os.path.join(self.output_config["output_directory"], filename)
        
        try:
            report_data = {
                "timestamp": timestamp,
                "summary": summary,
                "opportunities": [asdict(opp) for opp in opportunities],
                "raw_data": {
                    broker: [asdict(swap_data) for swap_data in swap_data_list]
                    for broker, swap_data_list in all_swap_data.items()
                }
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"JSON报告已保存: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"保存JSON报告失败: {str(e)}")
            return ""
    
    def save_all_reports(self, opportunities: List[ArbitrageOpportunity], 
                        summary: Dict, all_swap_data: Dict) -> Dict[str, str]:
        """保存所有格式的报告"""
        results = {}
        
        # 控制台输出
        self.print_console_report(opportunities, summary)
        
        # Excel报告
        excel_path = self.save_excel_report(opportunities, summary, all_swap_data)
        if excel_path:
            results["excel"] = excel_path
        
        # JSON报告
        json_path = self.save_json_report(opportunities, summary, all_swap_data)
        if json_path:
            results["json"] = json_path
        
        return results
    
    def print_connection_status(self, connection_results: Dict[str, bool]):
        """打印连接状态"""
        print("\n🔗 MT5连接状态:")
        print("-" * 40)
        
        for broker, status in connection_results.items():
            status_icon = "✅" if status else "❌"
            status_text = "成功" if status else "失败"
            print(f"   {status_icon} {broker}: {status_text}")
        
        successful_connections = sum(connection_results.values())
        total_connections = len(connection_results)
        print(f"\n总计: {successful_connections}/{total_connections} 个连接成功")
    
    def print_data_collection_summary(self, all_swap_data: Dict):
        """打印数据收集摘要"""
        print(f"\n📈 数据收集摘要:")
        print("-" * 40)
        
        total_symbols = 0
        for broker, swap_data_list in all_swap_data.items():
            count = len(swap_data_list)
            total_symbols += count
            print(f"   {broker}: {count} 个品种")
        
        print(f"\n总计收集: {total_symbols} 条数据记录")

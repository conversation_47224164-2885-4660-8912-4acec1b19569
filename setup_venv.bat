@echo off
echo ================================================================================
echo MT5多平台隔夜费查询系统 - 虚拟环境设置
echo MT5 Multi-Platform Swap Rate Analysis - Virtual Environment Setup
echo ================================================================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python
    echo    请先安装Python 3.8-3.13版本
    echo    下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

python --version
echo ✅ Python环境检查通过

echo.
echo 📁 检查虚拟环境...
if exist "venv" (
    echo ℹ️  虚拟环境已存在，跳过创建
) else (
    echo 🔧 创建虚拟环境...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ 虚拟环境创建失败
        pause
        exit /b 1
    )
    echo ✅ 虚拟环境创建成功
)

echo.
echo 🔄 激活虚拟环境...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ❌ 虚拟环境激活失败
    pause
    exit /b 1
)
echo ✅ 虚拟环境已激活

echo.
echo 📦 升级pip...
python -m pip install --upgrade pip

echo.
echo 📦 安装Python依赖包...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 依赖包安装失败
    echo.
    echo 尝试单独安装关键包...
    pip install MetaTrader5
    pip install pandas
    pip install numpy
    pip install openpyxl
    pip install colorama
)
echo ✅ 依赖包安装完成

echo.
echo 📁 创建必要目录...
if not exist "results" mkdir results
if not exist "logs" mkdir logs
echo ✅ 目录创建完成

echo.
echo 📋 复制配置文件...
if not exist "config.py" (
    copy "config_example.py" "config.py"
    echo ✅ 已创建config.py配置文件
    echo ⚠️  请编辑config.py文件，填入您的MT5账户信息
) else (
    echo ℹ️  config.py已存在，跳过复制
)

echo.
echo ================================================================================
echo 虚拟环境设置完成! 
echo ================================================================================
echo.
echo 虚拟环境位置: %CD%\venv
echo.
echo 下一步操作:
echo 1. 在PyCharm中设置项目解释器为: %CD%\venv\Scripts\python.exe
echo 2. 编辑 config.py 文件，填入您的MT5账户信息
echo 3. 运行 test_connection.py 测试连接
echo 4. 运行 main.py 开始分析
echo.
echo PyCharm解释器设置步骤:
echo - File → Settings → Project → Python Interpreter
echo - 点击齿轮图标 → Add → Existing environment
echo - 选择: %CD%\venv\Scripts\python.exe
echo.
echo 命令行使用方法:
echo - 激活虚拟环境: venv\Scripts\activate
echo - 运行程序: python main.py
echo - 退出虚拟环境: deactivate
echo.
pause

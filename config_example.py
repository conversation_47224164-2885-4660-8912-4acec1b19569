"""
MT5多平台隔夜费查询系统配置示例文件
Example configuration file for MT5 Multi-Platform Swap Rate Query System

请复制此文件为 config.py 并填入您的实际账户信息
Please copy this file to config.py and fill in your actual account information
"""

# MT5经纪商配置示例
# MT5 Broker Configuration Examples
MT5_BROKERS = {
    # ICMarkets 示例配置
    "ICMarkets": {
        "server": "ICMarkets-Demo",  # 服务器名称 (从MT5终端获取)
        "login": ********,           # 您的账户号码
        "password": "YourPassword123",  # 您的账户密码
        "path": "C:/Program Files/ICMarkets - MetaTrader 5/terminal64.exe",  # MT5安装路径
        "enabled": True              # 是否启用此经纪商
    },
    
    # EXNESS 示例配置
    "EXNESS": {
        "server": "Exness-MT5Trial",
        "login": ********,
        "password": "YourPassword456",
        "path": "C:/Program Files/MetaTrader 5 EXNESS/terminal64.exe",
        "enabled": True
    },
    
    # XM 示例配置
    "XM": {
        "server": "XMGlobal-MT5",
        "login": ********,
        "password": "YourPassword789",
        "path": "C:/Program Files/XM MT5/terminal64.exe",
        "enabled": False  # 暂时禁用
    },
    
    # FXPRO 示例配置
    "FXPRO": {
        "server": "FxPro-MT5Live",
        "login": 22222222,
        "password": "YourPasswordABC",
        "path": "C:/Program Files/FxPro - MetaTrader 5/terminal64.exe",
        "enabled": False
    },
    
    # Admiral Markets 示例配置
    "AdmiralMarkets": {
        "server": "AdmiralMarkets-Demo",
        "login": 33333333,
        "password": "YourPasswordDEF",
        "path": "C:/Program Files/Admiral Markets MT5/terminal64.exe",
        "enabled": False
    }
}

# 目标交易品种列表
# Target Trading Symbols List
# 如果为空列表 [] 则查询所有可用品种
# If empty list [], will query all available symbols
TARGET_SYMBOLS = [
    # 主要货币对 Major Currency Pairs
    "EURUSD", "GBPUSD", "USDJPY", "USDCHF", 
    "AUDUSD", "USDCAD", "NZDUSD",
    
    # 次要货币对 Minor Currency Pairs
    "EURJPY", "GBPJPY", "EURGBP", "EURAUD", 
    "EURCHF", "AUDJPY", "GBPAUD", "GBPCHF",
    
    # 贵金属 Precious Metals
    "XAUUSD", "XAGUSD", "XAUEUR", "XAGEUR",
    
    # 原油和能源 Oil and Energy
    "USOIL", "UKOIL", "NGAS",
    
    # 主要指数 Major Indices
    "US30", "US500", "NAS100", "GER30", 
    "UK100", "JPN225", "AUS200", "FRA40"
]

# 分析参数配置
# Analysis Parameters Configuration
ANALYSIS_CONFIG = {
    "min_swap_difference": 0.5,      # 最小隔夜费差异阈值 (点)
    "min_profit_potential": 1.0,     # 最小盈利潜力阈值 (每日)
    "max_spread_ratio": 0.3,         # 最大点差成本比率
    "currency_conversion": True,      # 是否进行货币转换
    "base_currency": "USD"           # 基准货币
}

# 输出配置
# Output Configuration
OUTPUT_CONFIG = {
    "console_output": True,          # 控制台输出
    "excel_output": True,            # Excel文件输出
    "json_output": True,             # JSON文件输出
    "output_directory": "./results/", # 输出目录
    "filename_prefix": "swap_analysis_"  # 文件名前缀
}

# 连接配置
# Connection Configuration
CONNECTION_CONFIG = {
    "timeout_seconds": 30,           # 连接超时时间(秒)
    "retry_attempts": 3,             # 重试次数
    "concurrent_connections": True,   # 是否使用并发连接
    "connection_delay": 2            # 连接间延迟(秒)
}

# 日志配置
# Logging Configuration
LOGGING_CONFIG = {
    "level": "INFO",                 # 日志级别: DEBUG, INFO, WARNING, ERROR
    "file_logging": True,            # 是否记录到文件
    "console_logging": True,         # 是否输出到控制台
    "log_directory": "./logs/"       # 日志文件目录
}

# 常见服务器名称参考
# Common Server Names Reference
"""
常见MT5服务器名称示例 (请从您的MT5终端获取准确名称):

ICMarkets:
- ICMarkets-Demo (模拟)
- ICMarkets-Live01, ICMarkets-Live02 (真实)

EXNESS:
- Exness-MT5Trial (模拟)
- Exness-MT5Real, Exness-MT5Real2 (真实)

XM:
- XMGlobal-Demo (模拟)
- XMGlobal-MT5, XMGlobal-MT5 2 (真实)

FXPRO:
- FxPro-MT5Demo (模拟)
- FxPro-MT5Live (真实)

Admiral Markets:
- AdmiralMarkets-Demo (模拟)
- AdmiralMarkets-Live (真实)

注意: 服务器名称可能会变化，请从您的MT5终端中获取最新的服务器列表
"""

# 安全提示
# Security Tips
"""
安全建议:
1. 不要将包含真实密码的config.py文件提交到版本控制系统
2. 考虑使用环境变量存储敏感信息
3. 定期更改密码
4. 使用模拟账户进行测试

Environment Variables Example:
import os
"password": os.getenv("MT5_PASSWORD", "default_password")
"""

"""
隔夜费分析模块
Swap Rate Analysis Module for identifying arbitrage opportunities
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass, asdict
from mt5_connector import SwapData
from config import ANALYSIS_CONFIG

@dataclass
class ArbitrageOpportunity:
    """套利机会数据结构"""
    symbol: str
    broker_long: str
    broker_short: str
    swap_long: float
    swap_short: float
    swap_difference: float
    profit_potential: float
    spread_cost: float
    net_profit_potential: float
    risk_level: str
    currency_base: str
    currency_profit: str
    recommendation: str

class SwapAnalyzer:
    """隔夜费分析器"""
    
    def __init__(self):
        self.logger = logging.getLogger("SwapAnalyzer")
        self.analysis_config = ANALYSIS_CONFIG
    
    def create_dataframe(self, all_swap_data: Dict[str, List[SwapData]]) -> pd.DataFrame:
        """将隔夜费数据转换为DataFrame"""
        data_rows = []
        
        for broker_name, swap_data_list in all_swap_data.items():
            for swap_data in swap_data_list:
                data_rows.append(asdict(swap_data))
        
        if not data_rows:
            self.logger.warning("没有可用的隔夜费数据")
            return pd.DataFrame()
        
        df = pd.DataFrame(data_rows)
        self.logger.info(f"创建了包含 {len(df)} 条记录的数据框")
        return df
    
    def find_arbitrage_opportunities(self, df: pd.DataFrame) -> List[ArbitrageOpportunity]:
        """寻找套利机会"""
        if df.empty:
            return []
        
        opportunities = []
        symbols = df['symbol'].unique()
        
        for symbol in symbols:
            symbol_data = df[df['symbol'] == symbol]
            if len(symbol_data) < 2:
                continue  # 需要至少两个经纪商的数据
            
            # 寻找最佳long和short组合
            opportunities.extend(self._analyze_symbol_opportunities(symbol_data))
        
        # 按盈利潜力排序
        opportunities.sort(key=lambda x: x.net_profit_potential, reverse=True)
        
        self.logger.info(f"发现 {len(opportunities)} 个潜在套利机会")
        return opportunities
    
    def _analyze_symbol_opportunities(self, symbol_data: pd.DataFrame) -> List[ArbitrageOpportunity]:
        """分析单个品种的套利机会"""
        opportunities = []
        symbol = symbol_data.iloc[0]['symbol']
        
        # 获取所有经纪商的数据
        brokers_data = {}
        for _, row in symbol_data.iterrows():
            brokers_data[row['broker']] = row
        
        # 比较所有经纪商组合
        broker_names = list(brokers_data.keys())
        for i, broker1 in enumerate(broker_names):
            for j, broker2 in enumerate(broker_names):
                if i >= j:
                    continue
                
                data1 = brokers_data[broker1]
                data2 = brokers_data[broker2]
                
                # 策略1: broker1做多，broker2做空
                opp1 = self._calculate_opportunity(
                    symbol, data1, data2, "long_short"
                )
                if opp1:
                    opportunities.append(opp1)
                
                # 策略2: broker1做空，broker2做多
                opp2 = self._calculate_opportunity(
                    symbol, data2, data1, "long_short"
                )
                if opp2:
                    opportunities.append(opp2)
        
        return opportunities
    
    def _calculate_opportunity(self, symbol: str, long_data: dict, short_data: dict, 
                             strategy: str) -> Optional[ArbitrageOpportunity]:
        """计算套利机会"""
        try:
            # 计算隔夜费差异
            swap_long = long_data['swap_long']
            swap_short = short_data['swap_short']
            swap_difference = swap_long + swap_short  # 注意：short通常为负值
            
            # 检查最小差异阈值
            if abs(swap_difference) < self.analysis_config['min_swap_difference']:
                return None
            
            # 计算点差成本
            spread_cost = (long_data['spread'] + short_data['spread']) * long_data['point']
            
            # 计算盈利潜力 (每日)
            profit_potential = abs(swap_difference)
            net_profit_potential = profit_potential - spread_cost * self.analysis_config['max_spread_ratio']
            
            # 检查最小盈利潜力
            if net_profit_potential < self.analysis_config['min_profit_potential']:
                return None
            
            # 评估风险等级
            risk_level = self._assess_risk_level(swap_difference, spread_cost, profit_potential)
            
            # 生成建议
            recommendation = self._generate_recommendation(
                swap_difference, net_profit_potential, risk_level
            )
            
            return ArbitrageOpportunity(
                symbol=symbol,
                broker_long=long_data['broker'],
                broker_short=short_data['broker'],
                swap_long=swap_long,
                swap_short=swap_short,
                swap_difference=swap_difference,
                profit_potential=profit_potential,
                spread_cost=spread_cost,
                net_profit_potential=net_profit_potential,
                risk_level=risk_level,
                currency_base=long_data['currency_base'],
                currency_profit=long_data['currency_profit'],
                recommendation=recommendation
            )
            
        except Exception as e:
            self.logger.error(f"计算套利机会异常 {symbol}: {str(e)}")
            return None
    
    def _assess_risk_level(self, swap_difference: float, spread_cost: float, 
                          profit_potential: float) -> str:
        """评估风险等级"""
        if profit_potential > spread_cost * 5:
            return "低风险"
        elif profit_potential > spread_cost * 2:
            return "中等风险"
        else:
            return "高风险"
    
    def _generate_recommendation(self, swap_difference: float, 
                               net_profit_potential: float, risk_level: str) -> str:
        """生成交易建议"""
        if net_profit_potential > 5 and risk_level == "低风险":
            return "强烈推荐"
        elif net_profit_potential > 2 and risk_level in ["低风险", "中等风险"]:
            return "推荐"
        elif net_profit_potential > 1:
            return "谨慎考虑"
        else:
            return "不推荐"
    
    def create_summary_report(self, opportunities: List[ArbitrageOpportunity]) -> Dict:
        """创建分析摘要报告"""
        if not opportunities:
            return {
                "total_opportunities": 0,
                "summary": "未发现套利机会"
            }
        
        df = pd.DataFrame([asdict(opp) for opp in opportunities])
        
        summary = {
            "total_opportunities": len(opportunities),
            "high_potential_count": len(df[df['net_profit_potential'] > 5]),
            "medium_potential_count": len(df[(df['net_profit_potential'] > 2) & (df['net_profit_potential'] <= 5)]),
            "low_potential_count": len(df[df['net_profit_potential'] <= 2]),
            "avg_profit_potential": df['net_profit_potential'].mean(),
            "max_profit_potential": df['net_profit_potential'].max(),
            "top_symbols": df.nlargest(5, 'net_profit_potential')['symbol'].tolist(),
            "risk_distribution": df['risk_level'].value_counts().to_dict(),
            "recommendation_distribution": df['recommendation'].value_counts().to_dict()
        }
        
        return summary

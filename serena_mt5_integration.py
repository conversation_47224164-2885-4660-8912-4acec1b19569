"""
Serena与MT5项目集成脚本
Serena and MT5 Project Integration Script

将Serena MCP服务器与MT5隔夜费查询系统集成
"""

import requests
import json
import logging
from typing import Dict, List, Optional
import MetaTrader5 as mt5
from datetime import datetime

class SerenaMT5Integration:
    """Serena与MT5集成类"""
    
    def __init__(self, serena_url: str = "http://localhost:3000"):
        self.serena_url = serena_url
        self.logger = logging.getLogger("SerenaMT5Integration")
        
    def check_serena_connection(self) -> bool:
        """检查Serena服务器连接"""
        try:
            response = requests.get(f"{self.serena_url}/health", timeout=5)
            if response.status_code == 200:
                self.logger.info("✅ Serena服务器连接成功")
                return True
            else:
                self.logger.error(f"❌ Serena服务器响应错误: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.logger.error(f"❌ 无法连接到Serena服务器: {e}")
            return False
    
    def send_swap_data_to_serena(self, swap_data: List[Dict]) -> bool:
        """将隔夜费数据发送到Serena"""
        try:
            payload = {
                "type": "mt5_swap_data",
                "timestamp": datetime.now().isoformat(),
                "data": swap_data
            }
            
            response = requests.post(
                f"{self.serena_url}/api/data",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                self.logger.info(f"✅ 成功发送 {len(swap_data)} 条隔夜费数据到Serena")
                return True
            else:
                self.logger.error(f"❌ 发送数据失败: {response.status_code} - {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"❌ 发送数据到Serena异常: {e}")
            return False
    
    def query_serena_analysis(self, query: str) -> Optional[Dict]:
        """向Serena查询分析结果"""
        try:
            payload = {
                "query": query,
                "context": "mt5_swap_analysis"
            }
            
            response = requests.post(
                f"{self.serena_url}/api/query",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                self.logger.info("✅ 成功获取Serena分析结果")
                return result
            else:
                self.logger.error(f"❌ 查询失败: {response.status_code}")
                return None
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"❌ 查询Serena异常: {e}")
            return None
    
    def get_mt5_data_and_send_to_serena(self):
        """获取MT5数据并发送到Serena"""
        print("🔗 连接到MT5...")
        
        # 使用您的Exness配置
        if not mt5.initialize():
            print("❌ MT5初始化失败")
            return False
        
        if not mt5.login(76905307, "A4.com123", "MT5Trial5.exness.com"):
            print("❌ MT5登录失败")
            return False
        
        print("✅ MT5连接成功")
        
        # 获取隔夜费数据
        print("📊 获取隔夜费数据...")
        symbols = mt5.symbols_get()
        swap_data = []
        
        for symbol in symbols[:50]:  # 限制前50个标的进行测试
            if mt5.symbol_select(symbol.name, True):
                symbol_info = mt5.symbol_info(symbol.name)
                if symbol_info:
                    data = {
                        'symbol': symbol.name,
                        'swap_long': symbol_info.swap_long,
                        'swap_short': symbol_info.swap_short,
                        'spread': symbol_info.spread,
                        'currency_base': symbol_info.currency_base,
                        'currency_profit': symbol_info.currency_profit,
                        'timestamp': datetime.now().isoformat()
                    }
                    swap_data.append(data)
        
        mt5.shutdown()
        print(f"✅ 获取了 {len(swap_data)} 个标的的数据")
        
        # 发送到Serena
        if self.check_serena_connection():
            return self.send_swap_data_to_serena(swap_data)
        else:
            print("❌ Serena服务器不可用")
            return False

def main():
    """主函数"""
    print("="*80)
    print("Serena与MT5集成测试")
    print("="*80)
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建集成实例
    integration = SerenaMT5Integration()
    
    # 检查Serena连接
    if not integration.check_serena_connection():
        print("\n❌ 请确保Serena服务器正在运行:")
        print("1. cd serena")
        print("2. python main.py  # 或相应的启动命令")
        print("3. 确保服务运行在 http://localhost:3000")
        return
    
    # 获取MT5数据并发送到Serena
    success = integration.get_mt5_data_and_send_to_serena()
    
    if success:
        print("\n🎉 集成测试成功!")
        
        # 测试查询功能
        print("\n🔍 测试Serena查询功能...")
        result = integration.query_serena_analysis("分析隔夜费最高的货币对")
        
        if result:
            print("✅ 查询结果:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print("⚠️  查询功能可能需要额外配置")
    else:
        print("\n❌ 集成测试失败")

if __name__ == "__main__":
    main()

"""
Serena MCP适配器
Serena MCP Adapter - 将Serena服务器适配为标准MCP服务器
"""

import json
import sys
import asyncio
import logging
from typing import Dict, List, Any, Optional
import requests
from datetime import datetime

class SerenaMCPAdapter:
    """Serena MCP适配器类"""
    
    def __init__(self, serena_url: str = "http://localhost:3000"):
        self.serena_url = serena_url
        self.logger = logging.getLogger("SerenaMCPAdapter")
        
        # MCP服务器信息
        self.server_info = {
            "name": "serena-mcp-adapter",
            "version": "1.0.0",
            "description": "Serena AI助手MCP适配器，集成MT5隔夜费分析功能",
            "capabilities": {
                "tools": True,
                "resources": True,
                "prompts": True
            }
        }
        
        # 可用工具定义
        self.tools = {
            "analyze_swap_rates": {
                "name": "analyze_swap_rates",
                "description": "分析MT5隔夜费数据并识别套利机会",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "brokers": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "要分析的经纪商列表"
                        },
                        "symbols": {
                            "type": "array", 
                            "items": {"type": "string"},
                            "description": "要分析的交易品种列表"
                        },
                        "min_profit": {
                            "type": "number",
                            "description": "最小盈利阈值"
                        }
                    }
                }
            },
            "get_market_data": {
                "name": "get_market_data",
                "description": "获取实时市场数据和隔夜费信息",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "symbol": {
                            "type": "string",
                            "description": "交易品种代码"
                        },
                        "broker": {
                            "type": "string", 
                            "description": "经纪商名称"
                        }
                    },
                    "required": ["symbol"]
                }
            },
            "generate_report": {
                "name": "generate_report",
                "description": "生成详细的隔夜费分析报告",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "format": {
                            "type": "string",
                            "enum": ["json", "html", "markdown"],
                            "description": "报告格式"
                        },
                        "include_charts": {
                            "type": "boolean",
                            "description": "是否包含图表"
                        }
                    }
                }
            }
        }
    
    async def handle_mcp_request(self, request: Dict) -> Dict:
        """处理MCP请求"""
        try:
            method = request.get("method")
            params = request.get("params", {})
            
            if method == "initialize":
                return await self.handle_initialize(params)
            elif method == "tools/list":
                return await self.handle_tools_list()
            elif method == "tools/call":
                return await self.handle_tool_call(params)
            elif method == "resources/list":
                return await self.handle_resources_list()
            elif method == "resources/read":
                return await self.handle_resource_read(params)
            else:
                return {
                    "error": {
                        "code": -32601,
                        "message": f"Method not found: {method}"
                    }
                }
                
        except Exception as e:
            self.logger.error(f"处理MCP请求异常: {e}")
            return {
                "error": {
                    "code": -32603,
                    "message": f"Internal error: {str(e)}"
                }
            }
    
    async def handle_initialize(self, params: Dict) -> Dict:
        """处理初始化请求"""
        return {
            "result": {
                "protocolVersion": "2024-11-05",
                "capabilities": self.server_info["capabilities"],
                "serverInfo": {
                    "name": self.server_info["name"],
                    "version": self.server_info["version"]
                }
            }
        }
    
    async def handle_tools_list(self) -> Dict:
        """返回可用工具列表"""
        return {
            "result": {
                "tools": list(self.tools.values())
            }
        }
    
    async def handle_tool_call(self, params: Dict) -> Dict:
        """处理工具调用"""
        tool_name = params.get("name")
        arguments = params.get("arguments", {})
        
        if tool_name == "analyze_swap_rates":
            return await self.analyze_swap_rates(arguments)
        elif tool_name == "get_market_data":
            return await self.get_market_data(arguments)
        elif tool_name == "generate_report":
            return await self.generate_report(arguments)
        else:
            return {
                "error": {
                    "code": -32602,
                    "message": f"Unknown tool: {tool_name}"
                }
            }
    
    async def analyze_swap_rates(self, args: Dict) -> Dict:
        """分析隔夜费率"""
        try:
            # 调用Serena API进行分析
            response = requests.post(
                f"{self.serena_url}/api/analyze",
                json={
                    "type": "swap_analysis",
                    "parameters": args
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "result": {
                        "content": [
                            {
                                "type": "text",
                                "text": f"隔夜费分析完成。发现 {result.get('opportunities_count', 0)} 个套利机会。"
                            },
                            {
                                "type": "text", 
                                "text": json.dumps(result, indent=2, ensure_ascii=False)
                            }
                        ]
                    }
                }
            else:
                return {
                    "error": {
                        "code": -32603,
                        "message": f"Serena API调用失败: {response.status_code}"
                    }
                }
                
        except Exception as e:
            return {
                "error": {
                    "code": -32603,
                    "message": f"分析异常: {str(e)}"
                }
            }
    
    async def get_market_data(self, args: Dict) -> Dict:
        """获取市场数据"""
        try:
            symbol = args.get("symbol")
            broker = args.get("broker", "all")
            
            response = requests.get(
                f"{self.serena_url}/api/market-data",
                params={"symbol": symbol, "broker": broker},
                timeout=15
            )
            
            if response.status_code == 200:
                data = response.json()
                return {
                    "result": {
                        "content": [
                            {
                                "type": "text",
                                "text": f"获取 {symbol} 的市场数据成功"
                            },
                            {
                                "type": "text",
                                "text": json.dumps(data, indent=2, ensure_ascii=False)
                            }
                        ]
                    }
                }
            else:
                return {
                    "error": {
                        "code": -32603,
                        "message": f"获取市场数据失败: {response.status_code}"
                    }
                }
                
        except Exception as e:
            return {
                "error": {
                    "code": -32603,
                    "message": f"获取数据异常: {str(e)}"
                }
            }
    
    async def generate_report(self, args: Dict) -> Dict:
        """生成报告"""
        try:
            format_type = args.get("format", "json")
            include_charts = args.get("include_charts", False)
            
            response = requests.post(
                f"{self.serena_url}/api/report",
                json={
                    "format": format_type,
                    "include_charts": include_charts,
                    "timestamp": datetime.now().isoformat()
                },
                timeout=60
            )
            
            if response.status_code == 200:
                report = response.json()
                return {
                    "result": {
                        "content": [
                            {
                                "type": "text",
                                "text": f"报告生成完成 (格式: {format_type})"
                            },
                            {
                                "type": "text",
                                "text": json.dumps(report, indent=2, ensure_ascii=False)
                            }
                        ]
                    }
                }
            else:
                return {
                    "error": {
                        "code": -32603,
                        "message": f"生成报告失败: {response.status_code}"
                    }
                }
                
        except Exception as e:
            return {
                "error": {
                    "code": -32603,
                    "message": f"生成报告异常: {str(e)}"
                }
            }
    
    async def handle_resources_list(self) -> Dict:
        """返回可用资源列表"""
        return {
            "result": {
                "resources": [
                    {
                        "uri": "serena://swap-data",
                        "name": "隔夜费数据",
                        "description": "实时隔夜费数据资源"
                    },
                    {
                        "uri": "serena://analysis-results", 
                        "name": "分析结果",
                        "description": "最新的套利分析结果"
                    }
                ]
            }
        }
    
    async def handle_resource_read(self, params: Dict) -> Dict:
        """读取资源内容"""
        uri = params.get("uri")
        
        if uri == "serena://swap-data":
            # 返回隔夜费数据
            return {
                "result": {
                    "contents": [
                        {
                            "uri": uri,
                            "mimeType": "application/json",
                            "text": json.dumps({"message": "隔夜费数据资源"}, ensure_ascii=False)
                        }
                    ]
                }
            }
        else:
            return {
                "error": {
                    "code": -32602,
                    "message": f"Unknown resource: {uri}"
                }
            }

async def main():
    """主函数 - MCP服务器入口点"""
    adapter = SerenaMCPAdapter()
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 读取stdin并处理MCP请求
    while True:
        try:
            line = sys.stdin.readline()
            if not line:
                break
                
            request = json.loads(line.strip())
            response = await adapter.handle_mcp_request(request)
            
            # 输出响应到stdout
            print(json.dumps(response))
            sys.stdout.flush()
            
        except json.JSONDecodeError:
            continue
        except Exception as e:
            error_response = {
                "error": {
                    "code": -32603,
                    "message": f"Server error: {str(e)}"
                }
            }
            print(json.dumps(error_response))
            sys.stdout.flush()

if __name__ == "__main__":
    asyncio.run(main())

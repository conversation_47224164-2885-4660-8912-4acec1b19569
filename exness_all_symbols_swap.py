"""
Exness所有标的隔夜费查询脚本
Exness All Symbols Swap Rates Query Script

连接到Exness并获取所有可用标的的隔夜费数据
"""

import MetaTrader5 as mt5
import pandas as pd
import time
from datetime import datetime

def connect_to_exness():
    """连接到Exness服务器"""
    EXNESS_CONFIG = {
        "login": 76905307,
        "password": "A4.com123",
        "server": "MT5Trial5.exness.com",  # 使用成功的方法3格式
        "path": "C:/Program Files/MetaTrader 5 EXNESS/terminal64.exe"
    }
    
    print("🔗 连接到Exness服务器...")
    print(f"   服务器: {EXNESS_CONFIG['server']}")
    print(f"   账户: {EXNESS_CONFIG['login']}")
    
    try:
        # 使用成功的方法3: initialize() + login()
        if mt5.initialize():
            if mt5.login(EXNESS_CONFIG["login"], EXNESS_CONFIG["password"], EXNESS_CONFIG["server"]):
                account_info = mt5.account_info()
                if account_info:
                    print(f"✅ 连接成功!")
                    print(f"   账户: {account_info.login}")
                    print(f"   服务器: {account_info.server}")
                    print(f"   公司: {account_info.company}")
                    print(f"   余额: {account_info.balance} {account_info.currency}")
                    return True
                else:
                    print("❌ 无法获取账户信息")
                    return False
            else:
                error = mt5.last_error()
                print(f"❌ 登录失败: {error}")
                return False
        else:
            error = mt5.last_error()
            print(f"❌ 初始化失败: {error}")
            return False
            
    except Exception as e:
        print(f"❌ 连接异常: {e}")
        return False

def get_all_symbols():
    """获取所有可用的交易标的"""
    print(f"\n📊 获取所有可用标的...")
    
    try:
        # 获取所有标的
        symbols = mt5.symbols_get()
        if symbols is None:
            print("❌ 无法获取标的列表")
            return []
        
        print(f"✅ 找到 {len(symbols)} 个标的")
        
        # 按类别分组
        symbol_groups = {}
        for symbol in symbols:
            # 根据标的名称推断类别
            symbol_name = symbol.name
            if any(pair in symbol_name for pair in ['USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD', 'CHF', 'NZD']):
                if len(symbol_name) == 6 and symbol_name.isalpha():
                    category = "外汇货币对"
                else:
                    category = "外汇相关"
            elif 'XAU' in symbol_name or 'XAG' in symbol_name or 'GOLD' in symbol_name or 'SILVER' in symbol_name:
                category = "贵金属"
            elif any(oil in symbol_name for oil in ['OIL', 'CRUDE', 'BRENT', 'WTI']):
                category = "原油"
            elif any(gas in symbol_name for gas in ['GAS', 'NGAS']):
                category = "天然气"
            elif any(index in symbol_name for index in ['US30', 'US500', 'NAS100', 'GER30', 'UK100', 'JPN225']):
                category = "股指"
            elif 'BTC' in symbol_name or 'ETH' in symbol_name or 'crypto' in symbol_name.lower():
                category = "加密货币"
            else:
                category = "其他"
            
            if category not in symbol_groups:
                symbol_groups[category] = []
            symbol_groups[category].append(symbol)
        
        # 打印分类统计
        print(f"\n📈 标的分类统计:")
        for category, symbols_list in symbol_groups.items():
            print(f"   {category}: {len(symbols_list)} 个")
        
        return symbols
        
    except Exception as e:
        print(f"❌ 获取标的列表异常: {e}")
        return []

def add_symbols_to_market_watch(symbols):
    """将所有标的添加到市场观察窗口"""
    print(f"\n👁️ 添加标的到市场观察窗口...")
    
    added_count = 0
    failed_count = 0
    
    for symbol in symbols:
        try:
            if mt5.symbol_select(symbol.name, True):
                added_count += 1
            else:
                failed_count += 1
                
        except Exception as e:
            failed_count += 1
            continue
    
    print(f"✅ 成功添加: {added_count} 个标的")
    if failed_count > 0:
        print(f"⚠️  添加失败: {failed_count} 个标的")
    
    return added_count

def get_swap_data_for_all_symbols(symbols):
    """获取所有标的的隔夜费数据"""
    print(f"\n💰 获取所有标的的隔夜费数据...")
    
    swap_data = []
    processed = 0
    
    for symbol in symbols:
        try:
            # 确保标的已选择
            if not mt5.symbol_select(symbol.name, True):
                continue
                
            # 获取标的信息
            symbol_info = mt5.symbol_info(symbol.name)
            if symbol_info is None:
                continue
            
            # 收集数据
            data = {
                'symbol': symbol.name,
                'description': symbol_info.description if hasattr(symbol_info, 'description') else '',
                'swap_long': symbol_info.swap_long,
                'swap_short': symbol_info.swap_short,
                'spread': symbol_info.spread,
                'digits': symbol_info.digits,
                'point': symbol_info.point,
                'currency_base': symbol_info.currency_base,
                'currency_profit': symbol_info.currency_profit,
                'currency_margin': symbol_info.currency_margin,
                'trade_mode': symbol_info.trade_mode,
                'min_lot': symbol_info.volume_min,
                'max_lot': symbol_info.volume_max,
                'lot_step': symbol_info.volume_step,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            swap_data.append(data)
            processed += 1
            
            # 显示进度
            if processed % 50 == 0:
                print(f"   已处理: {processed}/{len(symbols)} 个标的...")
                
        except Exception as e:
            continue
    
    print(f"✅ 成功获取 {len(swap_data)} 个标的的隔夜费数据")
    return swap_data

def analyze_and_display_swap_data(swap_data):
    """分析并显示隔夜费数据"""
    if not swap_data:
        print("❌ 没有隔夜费数据可显示")
        return
    
    print(f"\n📊 隔夜费数据分析")
    print("="*100)
    
    # 创建DataFrame
    df = pd.DataFrame(swap_data)
    
    # 基本统计
    print(f"\n📈 基本统计:")
    print(f"   总标的数量: {len(df)}")
    print(f"   有隔夜费的标的: {len(df[(df['swap_long'] != 0) | (df['swap_short'] != 0)])}")
    print(f"   平均Long Swap: {df['swap_long'].mean():.4f}")
    print(f"   平均Short Swap: {df['swap_short'].mean():.4f}")
    
    # 按类别分析
    print(f"\n🏷️ 按标的类型分析:")
    
    # 外汇货币对
    forex_symbols = df[df['symbol'].str.len() == 6]
    if not forex_symbols.empty:
        print(f"\n💱 外汇货币对 ({len(forex_symbols)} 个):")
        print(f"{'标的':<10} {'描述':<20} {'Long Swap':<12} {'Short Swap':<12} {'点差':<8} {'基础货币':<8}")
        print("-" * 80)
        for _, row in forex_symbols.head(20).iterrows():
            print(f"{row['symbol']:<10} {row['description'][:18]:<20} {row['swap_long']:<12.4f} {row['swap_short']:<12.4f} {row['spread']:<8} {row['currency_base']:<8}")
    
    # 贵金属
    metals = df[df['symbol'].str.contains('XAU|XAG|GOLD|SILVER', case=False, na=False)]
    if not metals.empty:
        print(f"\n🥇 贵金属 ({len(metals)} 个):")
        print(f"{'标的':<15} {'描述':<25} {'Long Swap':<12} {'Short Swap':<12} {'点差':<8}")
        print("-" * 80)
        for _, row in metals.iterrows():
            print(f"{row['symbol']:<15} {row['description'][:23]:<25} {row['swap_long']:<12.4f} {row['swap_short']:<12.4f} {row['spread']:<8}")
    
    # 原油
    oils = df[df['symbol'].str.contains('OIL|CRUDE|BRENT|WTI', case=False, na=False)]
    if not oils.empty:
        print(f"\n🛢️ 原油 ({len(oils)} 个):")
        print(f"{'标的':<15} {'描述':<25} {'Long Swap':<12} {'Short Swap':<12} {'点差':<8}")
        print("-" * 80)
        for _, row in oils.iterrows():
            print(f"{row['symbol']:<15} {row['description'][:23]:<25} {row['swap_long']:<12.4f} {row['swap_short']:<12.4f} {row['spread']:<8}")
    
    # 股指
    indices = df[df['symbol'].str.contains('US30|US500|NAS100|GER30|UK100|JPN225', case=False, na=False)]
    if not indices.empty:
        print(f"\n📈 股指 ({len(indices)} 个):")
        print(f"{'标的':<15} {'描述':<25} {'Long Swap':<12} {'Short Swap':<12} {'点差':<8}")
        print("-" * 80)
        for _, row in indices.iterrows():
            print(f"{row['symbol']:<15} {row['description'][:23]:<25} {row['swap_long']:<12.4f} {row['swap_short']:<12.4f} {row['spread']:<8}")
    
    # 最高和最低隔夜费
    print(f"\n🔝 隔夜费排行:")
    print(f"\n📊 Long Swap最高的10个标的:")
    top_long = df.nlargest(10, 'swap_long')[['symbol', 'description', 'swap_long', 'currency_profit']]
    for _, row in top_long.iterrows():
        print(f"   {row['symbol']:<12} {row['swap_long']:<10.4f} {row['currency_profit']:<5} - {row['description'][:30]}")
    
    print(f"\n📊 Short Swap最高的10个标的:")
    top_short = df.nlargest(10, 'swap_short')[['symbol', 'description', 'swap_short', 'currency_profit']]
    for _, row in top_short.iterrows():
        print(f"   {row['symbol']:<12} {row['swap_short']:<10.4f} {row['currency_profit']:<5} - {row['description'][:30]}")
    
    print(f"\n📊 Long Swap最低的10个标的:")
    bottom_long = df.nsmallest(10, 'swap_long')[['symbol', 'description', 'swap_long', 'currency_profit']]
    for _, row in bottom_long.iterrows():
        print(f"   {row['symbol']:<12} {row['swap_long']:<10.4f} {row['currency_profit']:<5} - {row['description'][:30]}")
    
    return df

def save_swap_data(df):
    """保存隔夜费数据到文件"""
    if df is None or df.empty:
        return
    
    try:
        # 创建结果目录
        import os
        if not os.path.exists('results'):
            os.makedirs('results')
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存为Excel
        excel_file = f'results/exness_all_symbols_swap_{timestamp}.xlsx'
        df.to_excel(excel_file, index=False, engine='openpyxl')
        print(f"\n💾 数据已保存到: {excel_file}")
        
        # 保存为CSV
        csv_file = f'results/exness_all_symbols_swap_{timestamp}.csv'
        df.to_csv(csv_file, index=False, encoding='utf-8-sig')
        print(f"💾 数据已保存到: {csv_file}")
        
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")

def main():
    """主函数"""
    print("="*100)
    print("Exness所有标的隔夜费查询")
    print("="*100)
    
    try:
        # 1. 连接到Exness
        if not connect_to_exness():
            return
        
        # 2. 获取所有标的
        symbols = get_all_symbols()
        if not symbols:
            return
        
        # 3. 添加标的到市场观察
        added_count = add_symbols_to_market_watch(symbols)
        if added_count == 0:
            print("❌ 没有标的被添加到市场观察")
            return
        
        # 4. 获取隔夜费数据
        swap_data = get_swap_data_for_all_symbols(symbols)
        if not swap_data:
            print("❌ 没有获取到隔夜费数据")
            return
        
        # 5. 分析和显示数据
        df = analyze_and_display_swap_data(swap_data)
        
        # 6. 保存数据
        save_swap_data(df)
        
        print(f"\n✅ 所有操作完成!")
        print(f"   总共处理了 {len(swap_data)} 个标的")
        print(f"   数据已保存到 results/ 目录")
        
    except Exception as e:
        print(f"❌ 程序执行异常: {e}")
        
    finally:
        # 断开连接
        try:
            mt5.shutdown()
            print(f"\n🔌 已断开MT5连接")
        except:
            pass

if __name__ == "__main__":
    main()
